# MPU6050集成产品需求文档 (PRD)

## 文档信息
- **版本**: v1.0
- **创建日期**: 2025-07-15
- **负责人**: Emma (产品经理)
- **项目**: motor无编码器 -> 智能平衡车升级

## 1. 背景与问题陈述

### 1.1 当前状态
- **现有功能**: 基础双电机PWM控制，固定50%占空比运行
- **局限性**: 无法感知姿态，无法实现智能平衡控制
- **用户痛点**: 只能做简单的电机测试，无法实现实际应用价值

### 1.2 升级目标
将简单的电机控制项目升级为**智能平衡车系统**，通过MPU6050传感器实现姿态感知和自动平衡控制。

## 2. 目标与成功指标

### 2.1 项目目标 (Objectives)
1. **O1**: 成功集成MPU6050传感器，实现实时姿态检测
2. **O2**: 实现基础平衡控制算法，车体能够自主保持直立
3. **O3**: 提供可调节的控制参数，支持不同场景优化

### 2.2 关键结果 (Key Results)
1. **KR1**: MPU6050数据读取成功率 ≥ 99%，数据更新频率 ≥ 200Hz
2. **KR2**: 平衡车能在±15°倾斜范围内自动恢复直立状态
3. **KR3**: 直立状态下角度偏差 ≤ ±2°，稳定时间 ≤ 3秒
4. **KR4**: 系统响应时间 ≤ 5ms，控制周期稳定

### 2.3 反向指标 (Counter Metrics)
- 系统不应出现高频振荡（>10Hz）
- 电机不应出现过热现象
- 不应影响原有电机控制的稳定性

## 3. 功能规格详述

### 3.1 核心功能模块

#### 3.1.1 MPU6050传感器驱动
**功能描述**: 实现MPU6050的初始化、配置和数据读取
**输入**: I2C通信指令
**输出**: 6轴传感器数据（加速度计+陀螺仪）
**性能要求**: 
- 数据读取频率: 200Hz
- 通信成功率: >99%
- 初始化时间: <500ms

#### 3.1.2 姿态解算模块
**功能描述**: 将原始传感器数据转换为准确的姿态角度
**算法选择**: 
- 基础版: 互补滤波算法
- 高级版: 卡尔曼滤波算法
**输入**: MPU6050原始数据
**输出**: 实时姿态角度（俯仰角）
**精度要求**: 角度精度 ±0.5°

#### 3.1.3 平衡控制算法
**功能描述**: 基于姿态角度计算电机控制输出
**控制策略**: 
- 直立环: PD控制器
- 速度环: PI控制器（可选）
**输入**: 目标角度、当前角度、角速度
**输出**: 电机PWM控制信号
**响应时间**: <5ms

#### 3.1.4 电机控制接口
**功能描述**: 将控制算法输出转换为电机驱动信号
**输入**: 控制算法输出值
**输出**: PWM占空比和方向控制
**安全机制**: 
- 角度过大时自动停机（>45°）
- PWM输出限幅保护

### 3.2 用户交互功能

#### 3.2.1 串口调试接口
**功能描述**: 通过串口实时查看系统状态和调节参数
**输出信息**: 
- 实时角度值
- 控制器输出
- 系统状态
**参数调节**: 支持PID参数在线调节

#### 3.2.2 LED状态指示
**功能描述**: 通过LED指示系统运行状态
**状态定义**:
- 常亮: 系统正常运行
- 慢闪: 初始化中
- 快闪: 错误状态
- 熄灭: 系统停止

## 4. 移植实施方案

### 4.1 代码移植步骤

#### 步骤1: 创建MPU6050驱动文件
```c
// 新建文件: Core/Src/mpu6050.c, Core/Inc/mpu6050.h
// 移植内容:
1. MPU6050寄存器定义
2. 数据结构定义  
3. 初始化函数
4. 数据读取函数
5. I2C通信适配层
```

#### 步骤2: 适配I2C通信函数
```c
// 原代码函数签名:
void MPU6050_WriteByte(uint8_t reg, uint8_t data);
void MPU6050_ReadBytes(uint8_t reg, uint8_t length, uint8_t* data);

// 适配为HAL库调用:
#define MPU6050_ADDRESS (0x68 << 1)
HAL_I2C_Mem_Write(&hi2c1, MPU6050_ADDRESS, reg, I2C_MEMADD_SIZE_8BIT, &data, 1, HAL_MAX_DELAY);
HAL_I2C_Mem_Read(&hi2c1, MPU6050_ADDRESS, reg, I2C_MEMADD_SIZE_8BIT, data, length, HAL_MAX_DELAY);
```

#### 步骤3: 集成姿态解算算法
```c
// 新建文件: Core/Src/attitude.c, Core/Inc/attitude.h
// 移植内容:
1. 互补滤波算法
2. 卡尔曼滤波算法（可选）
3. 角度计算函数
4. 滤波参数配置
```

#### 步骤4: 实现平衡控制算法
```c
// 新建文件: Core/Src/balance_control.c, Core/Inc/balance_control.h
// 移植内容:
1. PID控制器结构体
2. PID计算函数
3. 控制参数配置
4. 输出限幅保护
```

#### 步骤5: 修改主程序集成
```c
// 修改文件: Core/Src/main.c
// 集成内容:
1. 添加MPU6050初始化
2. 添加定时器中断控制循环
3. 集成平衡控制逻辑
4. 添加串口调试输出
```

### 4.2 硬件连接修改

#### 4.2.1 MPU6050连接
```
MPU6050模块 -> STM32F4开发板
VCC  -> 3.3V
GND  -> GND  
SCL  -> PB6 (I2C1_SCL)
SDA  -> PB7 (I2C1_SDA)
INT  -> PA0 (可选，数据就绪中断)
```

#### 4.2.2 引脚功能重新分配
```c
// 原有引脚功能保持:
PE9  -> 左电机PWM (TIM1_CH1)
PE11 -> 右电机PWM (TIM1_CH2)
PE12 -> AIN1 (左电机方向1)
PE14 -> AIN2 (左电机方向2)
PE8  -> BIN1 (右电机方向1)  
PE10 -> BIN2 (右电机方向2)

// 新增MPU6050功能:
PB6  -> I2C1_SCL (MPU6050时钟线)
PB7  -> I2C1_SDA (MPU6050数据线)
PA0  -> MPU6050_INT (可选中断)
```

## 5. 实现功能详述

### 5.1 基础功能 (第一阶段)

#### 5.1.1 传感器数据采集
- **实时读取**: 200Hz频率读取MPU6050数据
- **数据校验**: 检测I2C通信错误，数据有效性验证
- **串口输出**: 实时显示原始传感器数据

#### 5.1.2 姿态角度计算
- **互补滤波**: 融合加速度计和陀螺仪数据
- **角度输出**: 计算俯仰角（前后倾斜）
- **零点校准**: 支持传感器零点自动校准

#### 5.1.3 基础平衡控制
- **PD控制**: 实现简单的比例-微分控制
- **电机驱动**: 根据角度偏差控制电机转速和方向
- **安全保护**: 角度过大时自动停机

### 5.2 进阶功能 (第二阶段)

#### 5.2.1 高精度姿态解算
- **卡尔曼滤波**: 更精确的姿态估计
- **多轴融合**: 同时处理俯仰角和横滚角
- **动态校准**: 运行时自动校准陀螺仪零点

#### 5.2.2 串级PID控制
- **直立环**: 角度控制PID
- **速度环**: 速度控制PID  
- **参数自适应**: 根据运行状态自动调节参数

#### 5.2.3 智能控制功能
- **扰动恢复**: 外力推动后快速恢复平衡
- **启动保护**: 开机时平稳启动到平衡状态
- **低电压保护**: 电池电压过低时安全停机

### 5.3 扩展功能 (第三阶段)

#### 5.3.1 遥控功能
- **蓝牙控制**: 通过手机APP控制前进后退
- **方向控制**: 支持左转右转功能
- **速度调节**: 可调节移动速度

#### 5.3.2 智能避障
- **超声波传感器**: 检测前方障碍物
- **自动避障**: 遇到障碍物自动停止或转向
- **路径规划**: 简单的避障路径规划

#### 5.3.3 数据记录与分析
- **运行日志**: 记录运行数据到SD卡
- **性能分析**: 分析平衡性能和控制效果
- **参数优化**: 基于历史数据优化控制参数

## 6. 技术实现细节

### 6.1 关键代码示例

#### 6.1.1 MPU6050初始化
```c
HAL_StatusTypeDef MPU6050_Init(void) {
    uint8_t data;
    
    // 检测设备ID
    if(MPU6050_ReadByte(MPU6050_WHO_AM_I, &data) != HAL_OK) return HAL_ERROR;
    if(data != 0x68) return HAL_ERROR;
    
    // 复位设备
    MPU6050_WriteByte(MPU6050_PWR_MGMT_1, 0x80);
    HAL_Delay(100);
    
    // 配置设备
    MPU6050_WriteByte(MPU6050_PWR_MGMT_1, 0x01);  // 时钟源
    MPU6050_WriteByte(MPU6050_GYRO_CONFIG, 0x18); // 陀螺仪±2000°/s
    MPU6050_WriteByte(MPU6050_ACCEL_CONFIG, 0x00); // 加速度计±2g
    MPU6050_WriteByte(MPU6050_CONFIG, 0x03);       // 低通滤波44Hz
    
    return HAL_OK;
}
```

#### 6.1.2 平衡控制主循环
```c
void Balance_Control_Task(void) {
    // 读取传感器数据
    MPU6050_ReadData(&imu_data);
    
    // 姿态解算
    float pitch_angle = ComplementaryFilter(imu_data);
    
    // PID控制计算
    float control_output = PID_Calculate(0.0f, pitch_angle);
    
    // 电机控制输出
    Motor_SetSpeed(control_output);
    
    // 串口调试输出
    printf("Angle: %.2f, Output: %.2f\r\n", pitch_angle, control_output);
}
```

### 6.2 性能优化策略
- **中断驱动**: 使用定时器中断确保控制周期精确
- **DMA传输**: 使用DMA减少I2C通信的CPU占用
- **浮点优化**: 关键算法使用定点数运算提高速度
- **内存管理**: 合理分配静态内存，避免动态分配

## 7. 开发时间估算

### 7.1 详细工作量评估
- **MPU6050驱动移植**: 1天
- **姿态解算算法集成**: 1天  
- **平衡控制算法实现**: 2天
- **系统集成与调试**: 2天
- **参数调优与测试**: 1-2天
- **文档编写**: 0.5天

**总计**: 7.5-8.5天

### 7.2 里程碑计划
- **Day 1-2**: 完成基础驱动，能读取传感器数据
- **Day 3-4**: 完成姿态解算，能准确计算角度
- **Day 5-6**: 完成平衡控制，实现基础平衡功能
- **Day 7-8**: 系统优化，参数调节，达到稳定平衡

## 8. 风险评估与应对

### 8.1 技术风险
1. **I2C通信不稳定**: 检查硬件连接，调整通信参数
2. **姿态解算精度不足**: 调整滤波参数，考虑升级算法
3. **控制参数难以调节**: 提供参数调节工具，分步调试

### 8.2 硬件风险  
1. **传感器安装位置影响**: 确保传感器水平安装，避免振动
2. **电源噪声干扰**: 增加滤波电容，使用独立电源
3. **机械结构不稳定**: 确保车体重心合理，结构牢固

---
**PRD文档完成，等待技术团队开始实施。**
