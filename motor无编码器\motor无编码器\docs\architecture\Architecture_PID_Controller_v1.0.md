# PID平衡控制器系统架构设计文档

## 1. 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-01-15
- **负责人**: Bob (架构师)
- **项目名称**: STM32F4平衡车PID控制器架构设计
- **最后更新**: 2025-01-15

## 2. 系统架构概览

### 2.1 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    应用层 (Application Layer)                │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   平衡控制器     │  │   参数调节器     │  │   监控调试器     │ │
│  │ Balance Control │  │ Parameter Tuner │  │ Debug Monitor  │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    控制层 (Control Layer)                   │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   PID控制器     │  │   电机控制器     │  │   安全保护器     │ │
│  │ PID Controller  │  │ Motor Control   │  │ Safety Guard   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    感知层 (Perception Layer)                │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   姿态解算器     │  │   数据滤波器     │  │   传感器接口     │ │
│  │ Attitude Calc   │  │ Data Filter     │  │ Sensor Interface│ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    硬件层 (Hardware Layer)                  │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │     MPU6050     │  │    TB6612FNG    │  │     STM32F407   │ │
│  │   (I2C1接口)     │  │   (PWM+GPIO)    │  │   (主控制器)     │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 数据流架构
```
传感器数据 → 姿态解算 → PID控制 → 电机输出 → 物理反馈
    ↑                                           ↓
    └─────────── 闭环反馈控制系统 ←──────────────┘
```

## 3. 核心模块设计

### 3.1 PID控制器模块 (pid_controller.h/c)

#### 3.1.1 数据结构设计
```c
// PID控制器类型枚举
typedef enum {
    PID_TYPE_POSITIONAL = 0,  // 位置式PID
    PID_TYPE_INCREMENTAL = 1  // 增量式PID
} PID_Type_t;

// PID控制器状态枚举
typedef enum {
    PID_STATE_DISABLED = 0,   // 禁用状态
    PID_STATE_ENABLED = 1,    // 启用状态
    PID_STATE_SATURATED = 2   // 饱和状态
} PID_State_t;

// PID参数结构体
typedef struct {
    float Kp;           // 比例系数
    float Ki;           // 积分系数
    float Kd;           // 微分系数
    float max_output;   // 最大输出限制
    float min_output;   // 最小输出限制
    float max_integral; // 积分限幅
    float deadzone;     // 死区范围
} PID_Params_t;

// PID内部状态结构体
typedef struct {
    float error[3];     // 误差历史 [当前, 上次, 上上次]
    float integral;     // 积分累积值
    float derivative;   // 微分值
    float last_output;  // 上次输出值
    uint32_t last_time; // 上次计算时间
} PID_State_Data_t;

// PID控制器主结构体
typedef struct {
    PID_Type_t type;           // PID类型
    PID_State_t state;         // 控制器状态
    PID_Params_t params;       // PID参数
    PID_State_Data_t data;     // 内部状态数据
    float setpoint;            // 目标值
    float feedback;            // 反馈值
    float output;              // 输出值
    uint32_t update_count;     // 更新计数器
} PID_Controller_t;
```

#### 3.1.2 接口函数设计
```c
// 初始化函数
HAL_StatusTypeDef PID_Init(PID_Controller_t *pid, PID_Type_t type);
HAL_StatusTypeDef PID_SetParams(PID_Controller_t *pid, PID_Params_t *params);

// 控制函数
float PID_Update(PID_Controller_t *pid, float setpoint, float feedback, float dt);
void PID_Reset(PID_Controller_t *pid);
void PID_Enable(PID_Controller_t *pid);
void PID_Disable(PID_Controller_t *pid);

// 参数调节函数
void PID_SetKp(PID_Controller_t *pid, float kp);
void PID_SetKi(PID_Controller_t *pid, float ki);
void PID_SetKd(PID_Controller_t *pid, float kd);
void PID_SetOutputLimits(PID_Controller_t *pid, float min, float max);

// 状态查询函数
PID_State_t PID_GetState(PID_Controller_t *pid);
float PID_GetError(PID_Controller_t *pid);
float PID_GetIntegral(PID_Controller_t *pid);
float PID_GetDerivative(PID_Controller_t *pid);
```

### 3.2 平衡控制模块 (balance_control.h/c)

#### 3.2.1 数据结构设计
```c
// 平衡控制器配置
typedef struct {
    float target_angle;        // 目标平衡角度 (通常为0°)
    float angle_tolerance;     // 角度容差范围
    float max_tilt_angle;      // 最大允许倾斜角度
    float control_frequency;   // 控制频率 (Hz)
    uint8_t enable_integral_separation; // 积分分离使能
    float integral_separation_threshold; // 积分分离阈值
} Balance_Config_t;

// 平衡控制器状态
typedef struct {
    float current_angle;       // 当前角度
    float angle_error;         // 角度误差
    float control_output;      // 控制输出
    uint32_t control_cycles;   // 控制周期计数
    uint8_t is_balanced;       // 是否处于平衡状态
    uint8_t emergency_stop;    // 紧急停止标志
} Balance_Status_t;

// 平衡控制器主结构体
typedef struct {
    PID_Controller_t angle_pid;    // 角度环PID控制器
    Balance_Config_t config;       // 配置参数
    Balance_Status_t status;       // 状态信息
    Attitude_Data *attitude_data;  // 姿态数据指针
} Balance_Controller_t;
```

#### 3.2.2 接口函数设计
```c
// 初始化和配置
HAL_StatusTypeDef Balance_Init(Balance_Controller_t *balance);
HAL_StatusTypeDef Balance_SetConfig(Balance_Controller_t *balance, Balance_Config_t *config);
void Balance_AttachAttitudeData(Balance_Controller_t *balance, Attitude_Data *attitude);

// 控制函数
HAL_StatusTypeDef Balance_Update(Balance_Controller_t *balance);
void Balance_Start(Balance_Controller_t *balance);
void Balance_Stop(Balance_Controller_t *balance);
void Balance_EmergencyStop(Balance_Controller_t *balance);

// 状态查询
Balance_Status_t* Balance_GetStatus(Balance_Controller_t *balance);
uint8_t Balance_IsBalanced(Balance_Controller_t *balance);
float Balance_GetControlOutput(Balance_Controller_t *balance);
```

### 3.3 电机控制模块 (motor_control.h/c)

#### 3.3.1 数据结构设计
```c
// 电机方向枚举
typedef enum {
    MOTOR_DIR_STOP = 0,     // 停止
    MOTOR_DIR_FORWARD = 1,  // 前进
    MOTOR_DIR_BACKWARD = 2  // 后退
} Motor_Direction_t;

// 电机配置结构体
typedef struct {
    uint16_t max_pwm_value;     // 最大PWM值
    uint16_t min_pwm_value;     // 最小PWM值
    uint16_t deadzone_pwm;      // PWM死区
    float speed_limit;          // 速度限制
    uint8_t enable_soft_start;  // 软启动使能
} Motor_Config_t;

// 电机状态结构体
typedef struct {
    Motor_Direction_t direction;  // 当前方向
    uint16_t left_pwm;           // 左电机PWM值
    uint16_t right_pwm;          // 右电机PWM值
    float speed_command;         // 速度命令
    uint8_t is_enabled;          // 使能状态
    uint32_t fault_count;        // 故障计数
} Motor_Status_t;

// 电机控制器主结构体
typedef struct {
    Motor_Config_t config;       // 配置参数
    Motor_Status_t status;       // 状态信息
    TIM_HandleTypeDef *htim;     // 定时器句柄
    uint32_t pwm_channel_left;   // 左电机PWM通道
    uint32_t pwm_channel_right;  // 右电机PWM通道
} Motor_Controller_t;
```

#### 3.3.2 接口函数设计
```c
// 初始化和配置
HAL_StatusTypeDef Motor_Init(Motor_Controller_t *motor, TIM_HandleTypeDef *htim);
HAL_StatusTypeDef Motor_SetConfig(Motor_Controller_t *motor, Motor_Config_t *config);

// 控制函数
HAL_StatusTypeDef Motor_SetSpeed(Motor_Controller_t *motor, float speed);
HAL_StatusTypeDef Motor_SetDirection(Motor_Controller_t *motor, Motor_Direction_t direction);
void Motor_Stop(Motor_Controller_t *motor);
void Motor_EmergencyStop(Motor_Controller_t *motor);

// 状态查询
Motor_Status_t* Motor_GetStatus(Motor_Controller_t *motor);
uint8_t Motor_IsEnabled(Motor_Controller_t *motor);
uint16_t Motor_GetPWMValue(Motor_Controller_t *motor, uint8_t motor_id);
```

## 4. 系统集成架构

### 4.1 模块间接口关系
```c
// 主控制循环接口
typedef struct {
    Balance_Controller_t *balance;     // 平衡控制器
    Motor_Controller_t *motor;         // 电机控制器
    Attitude_Data *attitude;           // 姿态数据
    MPU6050_Data *sensor_data;         // 传感器数据
} System_Context_t;

// 系统状态枚举
typedef enum {
    SYSTEM_STATE_INIT = 0,        // 初始化状态
    SYSTEM_STATE_CALIBRATING = 1, // 校准状态
    SYSTEM_STATE_READY = 2,       // 就绪状态
    SYSTEM_STATE_RUNNING = 3,     // 运行状态
    SYSTEM_STATE_ERROR = 4        // 错误状态
} System_State_t;
```

### 4.2 时序控制架构
```c
// 控制时序配置
#define CONTROL_FREQUENCY_HZ    200     // 控制频率 200Hz
#define CONTROL_PERIOD_MS       5       // 控制周期 5ms
#define SENSOR_READ_FREQUENCY   200     // 传感器读取频率
#define DEBUG_OUTPUT_FREQUENCY  10      // 调试输出频率 10Hz

// 时序管理结构体
typedef struct {
    uint32_t control_timer;       // 控制定时器
    uint32_t sensor_timer;        // 传感器定时器
    uint32_t debug_timer;         // 调试定时器
    uint32_t system_start_time;   // 系统启动时间
} Timing_Manager_t;
```

## 5. 内存管理架构

### 5.1 静态内存分配
```c
// 全局控制器实例 (静态分配)
static Balance_Controller_t g_balance_controller;
static Motor_Controller_t g_motor_controller;
static PID_Controller_t g_angle_pid;
static System_Context_t g_system_context;
static Timing_Manager_t g_timing_manager;

// 内存使用估算
// PID_Controller_t:     ~100 bytes
// Balance_Controller_t: ~200 bytes  
// Motor_Controller_t:   ~150 bytes
// 总计:                 ~450 bytes
```

### 5.2 栈内存管理
```c
// 控制循环栈使用 (估算)
#define CONTROL_STACK_SIZE      512     // 控制任务栈大小
#define MAX_RECURSION_DEPTH     3       // 最大递归深度
#define TEMP_BUFFER_SIZE        64      // 临时缓冲区大小
```

## 6. 错误处理架构

### 6.1 错误代码定义
```c
// 系统错误代码
typedef enum {
    ERROR_NONE = 0,                 // 无错误
    ERROR_SENSOR_TIMEOUT = 1,       // 传感器超时
    ERROR_ANGLE_OVERFLOW = 2,       // 角度超限
    ERROR_MOTOR_FAULT = 3,          // 电机故障
    ERROR_PID_SATURATION = 4,       // PID饱和
    ERROR_SYSTEM_OVERLOAD = 5,      // 系统过载
    ERROR_INVALID_PARAMETER = 6     // 参数无效
} System_Error_t;

// 错误处理结构体
typedef struct {
    System_Error_t error_code;      // 错误代码
    uint32_t error_count;           // 错误计数
    uint32_t last_error_time;       // 最后错误时间
    char error_message[64];         // 错误消息
} Error_Handler_t;
```

### 6.2 安全保护机制
```c
// 安全保护配置
typedef struct {
    float max_angle_limit;          // 最大角度限制 (±45°)
    float max_angular_velocity;     // 最大角速度限制
    uint32_t watchdog_timeout_ms;   // 看门狗超时时间
    uint8_t enable_emergency_stop;  // 紧急停止使能
    uint8_t enable_soft_limits;     // 软限制使能
} Safety_Config_t;
```

## 7. 性能优化架构

### 7.1 计算优化
```c
// 快速数学函数 (查表法)
#define FAST_MATH_TABLE_SIZE    360
extern const float sin_table[FAST_MATH_TABLE_SIZE];
extern const float cos_table[FAST_MATH_TABLE_SIZE];

// 内联函数优化
static inline float fast_atan2f(float y, float x);
static inline float fast_sqrtf(float x);
```

### 7.2 实时性保证
```c
// 实时性配置
#define MAX_CONTROL_JITTER_US   100     // 最大控制抖动
#define PRIORITY_CONTROL_TASK   3       // 控制任务优先级
#define PRIORITY_SENSOR_TASK    2       // 传感器任务优先级
#define PRIORITY_DEBUG_TASK     1       // 调试任务优先级
```

## 8. 扩展性架构

### 8.1 插件接口设计
```c
// 控制算法插件接口
typedef struct {
    const char *name;                           // 算法名称
    HAL_StatusTypeDef (*init)(void *config);    // 初始化函数
    float (*update)(float error, float dt);    // 更新函数
    void (*reset)(void);                        // 重置函数
} Control_Algorithm_t;

// 传感器插件接口
typedef struct {
    const char *name;                           // 传感器名称
    HAL_StatusTypeDef (*init)(void);            // 初始化函数
    HAL_StatusTypeDef (*read)(void *data);      // 读取函数
    uint8_t (*is_ready)(void);                  // 就绪检查
} Sensor_Interface_t;
```

### 8.2 配置管理架构
```c
// 配置存储结构
typedef struct {
    uint32_t magic_number;          // 魔数校验
    uint16_t version;               // 配置版本
    uint16_t checksum;              // 校验和
    PID_Params_t pid_params;        // PID参数
    Balance_Config_t balance_config; // 平衡配置
    Motor_Config_t motor_config;    // 电机配置
    Safety_Config_t safety_config;  // 安全配置
} System_Config_t;

// 配置管理函数
HAL_StatusTypeDef Config_Save(System_Config_t *config);
HAL_StatusTypeDef Config_Load(System_Config_t *config);
HAL_StatusTypeDef Config_Reset(void);
```

## 9. 与现有系统集成方案

### 9.1 现有系统接口适配
```c
// 与现有MPU6050模块集成
extern MPU6050_Data g_mpu6050_data;        // 现有传感器数据
extern Attitude_Data g_attitude_data;      // 现有姿态数据

// 与现有定时器系统集成
extern TIM_HandleTypeDef htim1;            // 现有PWM定时器
extern TIM_HandleTypeDef htim2;            // 备用定时器

// 与现有GPIO系统集成
// 电机控制引脚 (已定义在main.h)
// AIN1_Pin, AIN2_Pin - 左电机方向控制
// BIN1_Pin, BIN2_Pin - 右电机方向控制
// PE9 (TIM1_CH1) - 左电机PWM
// PE11 (TIM1_CH2) - 右电机PWM
```

### 9.2 主程序集成架构
```c
// main.c 集成点
void System_Init(void) {
    // 现有初始化保持不变
    HAL_Init();
    SystemClock_Config();
    MX_GPIO_Init();
    MX_TIM1_Init();
    MX_I2C1_Init();
    MX_USART1_UART_Init();

    // 新增PID系统初始化
    PID_System_Init();
    Balance_System_Init();
    Motor_System_Init();
}

void System_Main_Loop(void) {
    while(1) {
        // 现有传感器读取 (保持不变)
        if(MPU6050_ReadData(&g_mpu6050_data) == HAL_OK) {
            Attitude_Update(&g_mpu6050_data, &g_attitude_data);

            // 新增平衡控制逻辑
            Balance_Control_Task();
        }

        HAL_Delay(5);  // 5ms控制周期
    }
}
```

### 9.3 兼容性保证
- **向后兼容**: 现有代码无需修改，新功能作为可选模块
- **渐进集成**: 支持分步骤集成，先基础功能后高级功能
- **配置灵活**: 通过编译开关控制功能启用/禁用

## 10. 开发优先级与里程碑

### 10.1 第一阶段 - 核心PID实现 (1小时)
- ✅ PID控制器基础数据结构
- ✅ 位置式PID算法实现
- ✅ 基本参数设置接口
- ✅ 简单测试验证

### 10.2 第二阶段 - 平衡控制集成 (45分钟)
- ✅ 平衡控制器框架
- ✅ 与姿态解算模块集成
- ✅ 电机控制接口实现
- ✅ 安全保护机制

### 10.3 第三阶段 - 高级功能 (30分钟)
- ✅ 增量式PID算法
- ✅ 参数自动调节
- ✅ 调试监控功能
- ✅ 性能优化

## 11. 质量保证架构

### 11.1 单元测试框架
```c
// 测试用例结构
typedef struct {
    const char *test_name;
    HAL_StatusTypeDef (*test_func)(void);
    uint8_t is_critical;
} Test_Case_t;

// 核心测试用例
Test_Case_t pid_test_cases[] = {
    {"PID_Init_Test", Test_PID_Init, 1},
    {"PID_Update_Test", Test_PID_Update, 1},
    {"PID_Limits_Test", Test_PID_Limits, 1},
    {"Balance_Control_Test", Test_Balance_Control, 1}
};
```

### 11.2 性能基准测试
```c
// 性能指标定义
#define MAX_CONTROL_LATENCY_US      50      // 最大控制延迟
#define MAX_CPU_USAGE_PERCENT       30      // 最大CPU使用率
#define MIN_CONTROL_FREQUENCY_HZ    180     // 最小控制频率
#define MAX_MEMORY_USAGE_BYTES      1024    // 最大内存使用
```

---

**文档状态**: ✅ 已完成
**架构设计要点**:
- 模块化设计，职责清晰分离
- 与现有系统无缝集成
- 支持多种PID算法和控制策略
- 完善的错误处理和安全保护
- 良好的扩展性和可维护性

**下一步**: 进入代码实现阶段
**负责人**: Bob → Alex
