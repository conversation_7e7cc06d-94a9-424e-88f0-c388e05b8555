/**
 ******************************************************************************
 * @file    balance_control.c
 * @brief   平衡控制器实现文件 - 基于PID的平衡车控制系统
 * <AUTHOR> (工程师)
 * @version v1.0
 * @date    2025-01-15
 ******************************************************************************
 */

/* Includes ------------------------------------------------------------------*/
#include "balance_control.h"
#include <stdio.h>
#include <string.h>

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/
static HAL_StatusTypeDef Balance_UpdatePIDParams(Balance_Controller_t *balance);
static float Balance_CalculateControlOutput(Balance_Controller_t *balance);
static void Balance_UpdateStatus(Balance_Controller_t *balance);
static uint8_t Balance_CheckSafetyLimits(Balance_Controller_t *balance);
static void Balance_HandleEmergency(Balance_Controller_t *balance, uint8_t fault_code);

/* Exported functions --------------------------------------------------------*/

/**
 * @brief  初始化平衡控制器
 * @param  balance: 平衡控制器指针
 * @retval HAL状态
 */
HAL_StatusTypeDef Balance_Init(Balance_Controller_t *balance)
{
    if (balance == NULL) {
        return HAL_ERROR;
    }
    
    // 清零结构体
    memset(balance, 0, sizeof(Balance_Controller_t));
    
    // 设置初始状态
    balance->state = BALANCE_STATE_INITIALIZING;
    balance->mode = BALANCE_MODE_ANGLE_ONLY;
    
    // 设置默认配置
    if (Balance_SetDefaultConfig(balance) != HAL_OK) {
        balance->state = BALANCE_STATE_ERROR;
        return HAL_ERROR;
    }
    
    // 初始化角度PID控制器
    if (PID_Init(&balance->angle_pid, PID_TYPE_POSITIONAL) != HAL_OK) {
        balance->state = BALANCE_STATE_ERROR;
        return HAL_ERROR;
    }
    
    // 设置PID参数
    PID_Params_t pid_params = {
        .Kp = BALANCE_DEFAULT_KP,
        .Ki = BALANCE_DEFAULT_KI,
        .Kd = BALANCE_DEFAULT_KD,
        .max_output = BALANCE_DEFAULT_MAX_OUTPUT,
        .min_output = BALANCE_DEFAULT_MIN_OUTPUT,
        .max_integral = 100.0f,
        .deadzone = 0.1f,
        .integral_separation_threshold = 5.0f,
        .enable_integral_separation = 1,
        .enable_derivative_on_measurement = 1
    };
    
    if (PID_SetParams(&balance->angle_pid, &pid_params) != HAL_OK) {
        balance->state = BALANCE_STATE_ERROR;
        return HAL_ERROR;
    }
    
    // 记录启动时间
    balance->start_time = HAL_GetTick();
    balance->last_update_time = balance->start_time;
    
    // 设置为就绪状态
    balance->state = BALANCE_STATE_READY;
    
    printf("Balance Controller: Initialized successfully\r\n");
    return HAL_OK;
}

/**
 * @brief  设置平衡控制器配置
 * @param  balance: 平衡控制器指针
 * @param  config: 配置参数指针
 * @retval HAL状态
 */
HAL_StatusTypeDef Balance_SetConfig(Balance_Controller_t *balance, const Balance_Config_t *config)
{
    if (balance == NULL || config == NULL) {
        return HAL_ERROR;
    }
    
    // 验证配置参数
    if (config->max_tilt_angle <= 0.0f || config->max_tilt_angle > 90.0f ||
        config->control_frequency <= 0.0f || config->control_frequency > 1000.0f) {
        return HAL_ERROR;
    }
    
    // 复制配置
    memcpy(&balance->config, config, sizeof(Balance_Config_t));
    
    return HAL_OK;
}

/**
 * @brief  设置默认配置
 * @param  balance: 平衡控制器指针
 * @retval HAL状态
 */
HAL_StatusTypeDef Balance_SetDefaultConfig(Balance_Controller_t *balance)
{
    if (balance == NULL) {
        return HAL_ERROR;
    }
    
    balance->config.target_angle = BALANCE_DEFAULT_TARGET_ANGLE;
    balance->config.angle_tolerance = BALANCE_DEFAULT_ANGLE_TOLERANCE;
    balance->config.max_tilt_angle = BALANCE_DEFAULT_MAX_TILT_ANGLE;
    balance->config.max_angular_velocity = BALANCE_DEFAULT_MAX_ANGULAR_VEL;
    balance->config.control_frequency = BALANCE_DEFAULT_CONTROL_FREQ;
    balance->config.balance_threshold = BALANCE_DEFAULT_BALANCE_THRESHOLD;
    balance->config.balance_stable_time = BALANCE_DEFAULT_STABLE_TIME;
    balance->config.enable_multi_level = 1;
    balance->config.enable_speed_compensation = 0;
    balance->config.enable_auto_recovery = 1;
    
    return HAL_OK;
}

/**
 * @brief  关联传感器数据
 * @param  balance: 平衡控制器指针
 * @param  sensor_data: 传感器数据指针
 * @param  attitude_data: 姿态数据指针
 * @retval None
 */
void Balance_AttachSensorData(Balance_Controller_t *balance, MPU6050_Data *sensor_data, Attitude_Data *attitude_data)
{
    if (balance != NULL) {
        balance->sensor_data = sensor_data;
        balance->attitude_data = attitude_data;
    }
}

/**
 * @brief  校准平衡控制器
 * @param  balance: 平衡控制器指针
 * @retval HAL状态
 */
HAL_StatusTypeDef Balance_Calibrate(Balance_Controller_t *balance)
{
    if (balance == NULL || balance->attitude_data == NULL) {
        return HAL_ERROR;
    }
    
    balance->state = BALANCE_STATE_CALIBRATING;
    
    printf("Balance Controller: Starting calibration...\r\n");
    
    // 校准目标角度 (使用当前角度作为平衡点)
    float calibration_samples = 0.0f;
    const int sample_count = 100;
    
    for (int i = 0; i < sample_count; i++) {
        calibration_samples += balance->attitude_data->pitch;
        HAL_Delay(10);
    }
    
    balance->config.target_angle = calibration_samples / sample_count;
    
    printf("Balance Controller: Calibration completed, target angle: %.2f°\r\n", 
           balance->config.target_angle);
    
    balance->state = BALANCE_STATE_READY;
    return HAL_OK;
}

/**
 * @brief  更新平衡控制器
 * @param  balance: 平衡控制器指针
 * @retval HAL状态
 */
HAL_StatusTypeDef Balance_Update(Balance_Controller_t *balance)
{
    if (balance == NULL || balance->state < BALANCE_STATE_READY) {
        return HAL_ERROR;
    }
    
    // 检查传感器数据有效性
    if (balance->attitude_data == NULL) {
        Balance_HandleEmergency(balance, BALANCE_ERROR_SENSOR_TIMEOUT);
        return HAL_ERROR;
    }
    
    // 计算时间间隔
    uint32_t current_time = HAL_GetTick();
    float dt = (current_time - balance->last_update_time) / 1000.0f;
    balance->last_update_time = current_time;
    
    // 更新当前状态
    balance->status.current_angle = balance->attitude_data->pitch;
    balance->status.angular_velocity = balance->attitude_data->gyro_rate_x;
    
    // 安全检查
    if (!Balance_CheckSafetyLimits(balance)) {
        return HAL_ERROR;
    }
    
    // 更新PID参数 (多级控制)
    Balance_UpdatePIDParams(balance);
    
    // 计算控制输出
    balance->status.control_output = Balance_CalculateControlOutput(balance);
    
    // 更新状态信息
    Balance_UpdateStatus(balance);
    
    balance->status.control_cycles++;
    
    return HAL_OK;
}

/**
 * @brief  启动平衡控制
 * @param  balance: 平衡控制器指针
 * @retval None
 */
void Balance_Start(Balance_Controller_t *balance)
{
    if (balance != NULL && balance->state == BALANCE_STATE_READY) {
        balance->state = BALANCE_STATE_RUNNING;
        balance->status.emergency_stop = 0;
        balance->status.fault_code = BALANCE_ERROR_NONE;
        
        // 重置PID控制器
        PID_Reset(&balance->angle_pid);
        PID_Enable(&balance->angle_pid);
        
        printf("Balance Controller: Started\r\n");
    }
}

/**
 * @brief  停止平衡控制
 * @param  balance: 平衡控制器指针
 * @retval None
 */
void Balance_Stop(Balance_Controller_t *balance)
{
    if (balance != NULL) {
        balance->state = BALANCE_STATE_READY;
        balance->status.control_output = 0.0f;
        
        // 禁用PID控制器
        PID_Disable(&balance->angle_pid);
        
        printf("Balance Controller: Stopped\r\n");
    }
}

/**
 * @brief  紧急停止
 * @param  balance: 平衡控制器指针
 * @retval None
 */
void Balance_EmergencyStop(Balance_Controller_t *balance)
{
    if (balance != NULL) {
        balance->state = BALANCE_STATE_EMERGENCY;
        balance->status.emergency_stop = 1;
        balance->status.control_output = 0.0f;
        
        // 禁用PID控制器
        PID_Disable(&balance->angle_pid);
        
        printf("Balance Controller: EMERGENCY STOP!\r\n");
    }
}

/**
 * @brief  重置平衡控制器
 * @param  balance: 平衡控制器指针
 * @retval None
 */
void Balance_Reset(Balance_Controller_t *balance)
{
    if (balance != NULL) {
        // 重置状态信息
        memset(&balance->status, 0, sizeof(Balance_Status_t));
        
        // 重置PID控制器
        PID_Reset(&balance->angle_pid);
        
        // 重置时间戳
        balance->last_update_time = HAL_GetTick();
        
        balance->state = BALANCE_STATE_READY;
        
        printf("Balance Controller: Reset\r\n");
    }
}

/**
 * @brief  设置目标角度
 * @param  balance: 平衡控制器指针
 * @param  target_angle: 目标角度 (度)
 * @retval None
 */
void Balance_SetTargetAngle(Balance_Controller_t *balance, float target_angle)
{
    if (balance != NULL && fabsf(target_angle) <= balance->config.max_tilt_angle) {
        balance->config.target_angle = target_angle;
    }
}

/**
 * @brief  设置PID参数
 * @param  balance: 平衡控制器指针
 * @param  kp: 比例系数
 * @param  ki: 积分系数
 * @param  kd: 微分系数
 * @retval None
 */
void Balance_SetPIDParams(Balance_Controller_t *balance, float kp, float ki, float kd)
{
    if (balance != NULL) {
        PID_SetKp(&balance->angle_pid, kp);
        PID_SetKi(&balance->angle_pid, ki);
        PID_SetKd(&balance->angle_pid, kd);
    }
}

/**
 * @brief  设置控制模式
 * @param  balance: 平衡控制器指针
 * @param  mode: 控制模式
 * @retval None
 */
void Balance_SetControlMode(Balance_Controller_t *balance, Balance_Mode_t mode)
{
    if (balance != NULL) {
        balance->mode = mode;
    }
}

/* Status Query Functions ----------------------------------------------------*/

/**
 * @brief  获取平衡控制器状态
 * @param  balance: 平衡控制器指针
 * @retval 状态信息指针
 */
Balance_Status_t* Balance_GetStatus(Balance_Controller_t *balance)
{
    if (balance == NULL) {
        return NULL;
    }
    return &balance->status;
}

/**
 * @brief  获取控制器状态
 * @param  balance: 平衡控制器指针
 * @retval 控制器状态
 */
Balance_State_t Balance_GetState(const Balance_Controller_t *balance)
{
    if (balance == NULL) {
        return BALANCE_STATE_ERROR;
    }
    return balance->state;
}

/**
 * @brief  检查是否处于平衡状态
 * @param  balance: 平衡控制器指针
 * @retval 1: 平衡, 0: 不平衡
 */
uint8_t Balance_IsBalanced(const Balance_Controller_t *balance)
{
    if (balance == NULL) {
        return 0;
    }
    return balance->status.is_balanced;
}

/**
 * @brief  获取控制输出
 * @param  balance: 平衡控制器指针
 * @retval 控制输出值
 */
float Balance_GetControlOutput(const Balance_Controller_t *balance)
{
    if (balance == NULL) {
        return 0.0f;
    }
    return balance->status.control_output;
}

/**
 * @brief  获取当前角度
 * @param  balance: 平衡控制器指针
 * @retval 当前角度 (度)
 */
float Balance_GetCurrentAngle(const Balance_Controller_t *balance)
{
    if (balance == NULL) {
        return 0.0f;
    }
    return balance->status.current_angle;
}

/**
 * @brief  获取平衡时间
 * @param  balance: 平衡控制器指针
 * @retval 连续平衡时间 (ms)
 */
uint32_t Balance_GetBalanceTime(const Balance_Controller_t *balance)
{
    if (balance == NULL) {
        return 0;
    }
    return balance->status.balance_time;
}

/* Utility Functions ---------------------------------------------------------*/

/**
 * @brief  打印平衡控制器状态
 * @param  balance: 平衡控制器指针
 * @retval None
 */
void Balance_PrintStatus(const Balance_Controller_t *balance)
{
    if (balance == NULL) {
        printf("Balance: NULL pointer\r\n");
        return;
    }

    printf("=== Balance Status ===\r\n");
    printf("State: %s\r\n", Balance_GetStateString(balance->state));
    printf("Mode: %d\r\n", balance->mode);
    printf("Angle: Current=%.2f°, Target=%.2f°, Error=%.2f°\r\n",
           balance->status.current_angle, balance->config.target_angle, balance->status.angle_error);
    printf("Angular Velocity: %.2f°/s\r\n", balance->status.angular_velocity);
    printf("Control Output: %.2f\r\n", balance->status.control_output);
    printf("Balanced: %s, Time: %lums\r\n",
           balance->status.is_balanced ? "Yes" : "No", balance->status.balance_time);
    printf("Cycles: %lu, Fault: %d\r\n", balance->status.control_cycles, balance->status.fault_code);
    printf("======================\r\n");
}

/**
 * @brief  获取状态字符串
 * @param  state: 状态枚举
 * @retval 状态字符串
 */
const char* Balance_GetStateString(Balance_State_t state)
{
    switch (state) {
        case BALANCE_STATE_DISABLED:     return "DISABLED";
        case BALANCE_STATE_INITIALIZING: return "INITIALIZING";
        case BALANCE_STATE_CALIBRATING:  return "CALIBRATING";
        case BALANCE_STATE_READY:        return "READY";
        case BALANCE_STATE_RUNNING:      return "RUNNING";
        case BALANCE_STATE_EMERGENCY:    return "EMERGENCY";
        case BALANCE_STATE_ERROR:        return "ERROR";
        default:                         return "UNKNOWN";
    }
}

/**
 * @brief  自检测试
 * @param  balance: 平衡控制器指针
 * @retval HAL状态
 */
HAL_StatusTypeDef Balance_SelfTest(Balance_Controller_t *balance)
{
    if (balance == NULL) {
        return HAL_ERROR;
    }

    // 检查PID控制器
    if (PID_SelfTest(&balance->angle_pid) != HAL_OK) {
        return HAL_ERROR;
    }

    // 检查传感器数据连接
    if (balance->attitude_data == NULL || balance->sensor_data == NULL) {
        return HAL_ERROR;
    }

    printf("Balance Controller: Self-test passed\r\n");
    return HAL_OK;
}

/* Private Functions ---------------------------------------------------------*/

/**
 * @brief  更新PID参数 (多级控制)
 * @param  balance: 平衡控制器指针
 * @retval HAL状态
 */
static HAL_StatusTypeDef Balance_UpdatePIDParams(Balance_Controller_t *balance)
{
    if (!balance->config.enable_multi_level) {
        return HAL_OK; // 不使用多级控制
    }

    float error_abs = fabsf(balance->status.angle_error);

    // 根据误差大小选择不同的PID参数
    if (error_abs > 10.0f) {
        // 粗调阶段 - 大误差快速响应
        PID_SetKp(&balance->angle_pid, 20.0f);
        PID_SetKi(&balance->angle_pid, 0.2f);
        PID_SetKd(&balance->angle_pid, 1.2f);
    } else if (error_abs > 2.0f) {
        // 精调阶段 - 中等误差平衡响应
        PID_SetKp(&balance->angle_pid, 15.0f);
        PID_SetKi(&balance->angle_pid, 0.5f);
        PID_SetKd(&balance->angle_pid, 0.8f);
    } else {
        // 稳态阶段 - 小误差精确控制
        PID_SetKp(&balance->angle_pid, 12.0f);
        PID_SetKi(&balance->angle_pid, 0.8f);
        PID_SetKd(&balance->angle_pid, 0.5f);
    }

    return HAL_OK;
}

/**
 * @brief  计算控制输出
 * @param  balance: 平衡控制器指针
 * @retval 控制输出值
 */
static float Balance_CalculateControlOutput(Balance_Controller_t *balance)
{
    // 计算角度误差
    balance->status.angle_error = balance->config.target_angle - balance->status.current_angle;

    // 计算时间间隔
    uint32_t current_time = HAL_GetTick();
    float dt = (current_time - balance->last_update_time) / 1000.0f;

    // PID控制计算
    float output = PID_Update(&balance->angle_pid,
                             balance->config.target_angle,
                             balance->status.current_angle,
                             dt);

    return output;
}

/**
 * @brief  更新状态信息
 * @param  balance: 平衡控制器指针
 * @retval None
 */
static void Balance_UpdateStatus(Balance_Controller_t *balance)
{
    uint32_t current_time = HAL_GetTick();

    // 检查是否处于平衡状态
    if (fabsf(balance->status.angle_error) <= balance->config.balance_threshold) {
        if (!balance->status.is_balanced) {
            balance->status.last_balance_time = current_time;
            balance->status.is_balanced = 1;
        }
        balance->status.balance_time = current_time - balance->status.last_balance_time;
    } else {
        balance->status.is_balanced = 0;
        balance->status.balance_time = 0;
    }
}

/**
 * @brief  检查安全限制
 * @param  balance: 平衡控制器指针
 * @retval 1: 安全, 0: 不安全
 */
static uint8_t Balance_CheckSafetyLimits(Balance_Controller_t *balance)
{
    // 检查角度限制
    if (fabsf(balance->status.current_angle) > balance->config.max_tilt_angle) {
        Balance_HandleEmergency(balance, BALANCE_ERROR_ANGLE_OVERFLOW);
        return 0;
    }

    // 检查角速度限制
    if (fabsf(balance->status.angular_velocity) > balance->config.max_angular_velocity) {
        Balance_HandleEmergency(balance, BALANCE_ERROR_ANGULAR_VEL_OVERFLOW);
        return 0;
    }

    return 1;
}

/**
 * @brief  处理紧急情况
 * @param  balance: 平衡控制器指针
 * @param  fault_code: 故障代码
 * @retval None
 */
static void Balance_HandleEmergency(Balance_Controller_t *balance, uint8_t fault_code)
{
    balance->status.fault_code = fault_code;
    Balance_EmergencyStop(balance);

    printf("Balance Emergency: Fault code %d\r\n", fault_code);
}

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
