# MPU6050移植实施指南

## 文档信息
- **版本**: v1.0
- **创建日期**: 2025-07-15
- **负责人**: <PERSON> (开发工程师)
- **项目**: MPU6050代码移植与功能实现

## 1. 移植准备工作

### 1.1 文件结构规划
```
Core/
├── Inc/
│   ├── mpu6050.h          # MPU6050驱动头文件
│   ├── attitude.h         # 姿态解算头文件
│   ├── balance_control.h  # 平衡控制头文件
│   └── config.h           # 配置参数头文件
└── Src/
    ├── mpu6050.c          # MPU6050驱动实现
    ├── attitude.c         # 姿态解算实现
    ├── balance_control.c  # 平衡控制实现
    └── main.c             # 主程序(需修改)
```

### 1.2 依赖检查
- ✅ I2C1已配置 (400kHz, 7位地址模式)
- ✅ TIM1 PWM已配置 (电机控制)
- ✅ USART1已配置 (调试串口)
- ✅ 系统时钟已配置 (168MHz)

## 2. 第一步：创建MPU6050驱动

### 2.1 创建头文件 (Core/Inc/mpu6050.h)
```c
#ifndef __MPU6050_H
#define __MPU6050_H

#include "main.h"
#include "i2c.h"

// MPU6050设备地址
#define MPU6050_ADDRESS     (0x68 << 1)

// MPU6050寄存器地址
#define MPU6050_WHO_AM_I    0x75
#define MPU6050_PWR_MGMT_1  0x6B
#define MPU6050_GYRO_CONFIG 0x1B
#define MPU6050_ACCEL_CONFIG 0x1C
#define MPU6050_CONFIG      0x1A
#define MPU6050_ACCEL_XOUT_H 0x3B

// 数据结构
typedef struct {
    int16_t Accel_X, Accel_Y, Accel_Z;
    int16_t Temp;
    int16_t Gyro_X, Gyro_Y, Gyro_Z;
} MPU6050_Data;

// 函数声明
HAL_StatusTypeDef MPU6050_Init(void);
HAL_StatusTypeDef MPU6050_ReadData(MPU6050_Data *data);
HAL_StatusTypeDef MPU6050_WriteByte(uint8_t reg, uint8_t data);
HAL_StatusTypeDef MPU6050_ReadByte(uint8_t reg, uint8_t *data);

#endif
```

### 2.2 创建驱动实现 (Core/Src/mpu6050.c)
```c
#include "mpu6050.h"
#include <stdio.h>

// 写入单个字节
HAL_StatusTypeDef MPU6050_WriteByte(uint8_t reg, uint8_t data) {
    return HAL_I2C_Mem_Write(&hi2c1, MPU6050_ADDRESS, reg, 
                            I2C_MEMADD_SIZE_8BIT, &data, 1, HAL_MAX_DELAY);
}

// 读取单个字节
HAL_StatusTypeDef MPU6050_ReadByte(uint8_t reg, uint8_t *data) {
    return HAL_I2C_Mem_Read(&hi2c1, MPU6050_ADDRESS, reg, 
                           I2C_MEMADD_SIZE_8BIT, data, 1, HAL_MAX_DELAY);
}

// 读取多个字节
HAL_StatusTypeDef MPU6050_ReadBytes(uint8_t reg, uint8_t length, uint8_t *data) {
    return HAL_I2C_Mem_Read(&hi2c1, MPU6050_ADDRESS, reg, 
                           I2C_MEMADD_SIZE_8BIT, data, length, HAL_MAX_DELAY);
}

// MPU6050初始化
HAL_StatusTypeDef MPU6050_Init(void) {
    uint8_t data;
    
    // 检测设备ID
    if(MPU6050_ReadByte(MPU6050_WHO_AM_I, &data) != HAL_OK) {
        printf("MPU6050: I2C通信失败\r\n");
        return HAL_ERROR;
    }
    
    if(data != 0x68) {
        printf("MPU6050: 设备ID错误 (0x%02X)\r\n", data);
        return HAL_ERROR;
    }
    
    // 复位设备
    MPU6050_WriteByte(MPU6050_PWR_MGMT_1, 0x80);
    HAL_Delay(100);
    
    // 配置电源管理 - 选择X轴陀螺仪作为时钟源
    MPU6050_WriteByte(MPU6050_PWR_MGMT_1, 0x01);
    
    // 配置陀螺仪量程 ±2000°/s
    MPU6050_WriteByte(MPU6050_GYRO_CONFIG, 0x18);
    
    // 配置加速度计量程 ±2g
    MPU6050_WriteByte(MPU6050_ACCEL_CONFIG, 0x00);
    
    // 配置低通滤波器 44Hz
    MPU6050_WriteByte(MPU6050_CONFIG, 0x03);
    
    printf("MPU6050: 初始化成功\r\n");
    return HAL_OK;
}

// 读取传感器数据
HAL_StatusTypeDef MPU6050_ReadData(MPU6050_Data *data) {
    uint8_t buf[14];
    
    if(MPU6050_ReadBytes(MPU6050_ACCEL_XOUT_H, 14, buf) != HAL_OK) {
        return HAL_ERROR;
    }
    
    // 解析数据 (大端序转小端序)
    data->Accel_X = (int16_t)(buf[0] << 8 | buf[1]);
    data->Accel_Y = (int16_t)(buf[2] << 8 | buf[3]);
    data->Accel_Z = (int16_t)(buf[4] << 8 | buf[5]);
    data->Temp = (int16_t)(buf[6] << 8 | buf[7]);
    data->Gyro_X = (int16_t)(buf[8] << 8 | buf[9]);
    data->Gyro_Y = (int16_t)(buf[10] << 8 | buf[11]);
    data->Gyro_Z = (int16_t)(buf[12] << 8 | buf[13]);
    
    return HAL_OK;
}
```

## 3. 第二步：创建姿态解算模块

### 3.1 创建头文件 (Core/Inc/attitude.h)
```c
#ifndef __ATTITUDE_H
#define __ATTITUDE_H

#include "main.h"
#include "mpu6050.h"
#include <math.h>

// 数学常量
#define PI 3.14159265359f
#define RAD_TO_DEG 57.2957795131f
#define DEG_TO_RAD 0.0174532925f

// 滤波参数
#define COMPLEMENTARY_ALPHA 0.98f
#define SAMPLE_TIME 0.005f  // 5ms采样周期

// 卡尔曼滤波器结构
typedef struct {
    float Q_angle;   // 过程噪声协方差
    float Q_bias;    // 过程噪声协方差  
    float R_measure; // 测量噪声协方差
    float angle;     // 计算得到的最优角度
    float bias;      // 陀螺仪偏置
    float P[2][2];   // 误差协方差矩阵
} KalmanFilter;

// 函数声明
float ComplementaryFilter(float accelAngle, float gyroRate, float dt, float alpha);
float Kalman_Update(KalmanFilter *kf, float newAngle, float newRate, float dt);
void Kalman_Init(KalmanFilter *kf);
float Calculate_Pitch_Angle(MPU6050_Data *data);
void Attitude_Init(void);

#endif
```

### 3.2 创建姿态解算实现 (Core/Src/attitude.c)
```c
#include "attitude.h"

static KalmanFilter pitch_kalman;

// 互补滤波算法
float ComplementaryFilter(float accelAngle, float gyroRate, float dt, float alpha) {
    static float angle = 0;
    angle = alpha * (angle + gyroRate * dt) + (1 - alpha) * accelAngle;
    return angle;
}

// 卡尔曼滤波器初始化
void Kalman_Init(KalmanFilter *kf) {
    kf->Q_angle = 0.001f;
    kf->Q_bias = 0.003f;
    kf->R_measure = 0.03f;
    kf->angle = 0.0f;
    kf->bias = 0.0f;
    kf->P[0][0] = 0.0f;
    kf->P[0][1] = 0.0f;
    kf->P[1][0] = 0.0f;
    kf->P[1][1] = 0.0f;
}

// 卡尔曼滤波更新
float Kalman_Update(KalmanFilter *kf, float newAngle, float newRate, float dt) {
    // 预测步骤
    kf->angle += dt * (newRate - kf->bias);
    kf->P[0][0] += dt * (dt*kf->P[1][1] - kf->P[0][1] - kf->P[1][0] + kf->Q_angle);
    kf->P[0][1] -= dt * kf->P[1][1];
    kf->P[1][0] -= dt * kf->P[1][1];
    kf->P[1][1] += kf->Q_bias * dt;

    // 更新步骤
    float y = newAngle - kf->angle;
    float S = kf->P[0][0] + kf->R_measure;
    float K[2];
    K[0] = kf->P[0][0] / S;
    K[1] = kf->P[1][0] / S;

    // 更新估计值
    kf->angle += K[0] * y;
    kf->bias += K[1] * y;

    // 更新协方差矩阵
    float P00_temp = kf->P[0][0];
    float P01_temp = kf->P[0][1];

    kf->P[0][0] -= K[0] * P00_temp;
    kf->P[0][1] -= K[0] * P01_temp;
    kf->P[1][0] -= K[1] * P00_temp;
    kf->P[1][1] -= K[1] * P01_temp;

    return kf->angle;
}

// 计算俯仰角
float Calculate_Pitch_Angle(MPU6050_Data *data) {
    // 加速度计角度计算
    float accel_angle = atan2f(data->Accel_Y, data->Accel_Z) * RAD_TO_DEG;
    
    // 陀螺仪角速度 (转换为度/秒)
    float gyro_rate = data->Gyro_X / 16.4f;  // ±2000°/s量程对应16.4 LSB/°/s
    
    // 使用互补滤波
    return ComplementaryFilter(accel_angle, gyro_rate, SAMPLE_TIME, COMPLEMENTARY_ALPHA);
    
    // 或者使用卡尔曼滤波 (注释掉上面一行，启用下面一行)
    // return Kalman_Update(&pitch_kalman, accel_angle, gyro_rate, SAMPLE_TIME);
}

// 姿态解算初始化
void Attitude_Init(void) {
    Kalman_Init(&pitch_kalman);
}
```

## 4. 第三步：创建平衡控制模块

### 4.1 创建头文件 (Core/Inc/balance_control.h)
```c
#ifndef __BALANCE_CONTROL_H
#define __BALANCE_CONTROL_H

#include "main.h"

// PID控制器结构
typedef struct {
    float Kp, Ki, Kd;
    float integral;
    float prevError;
    float integralLimit;
    float outputLimit;
} PID_Controller;

// 控制参数
#define TARGET_ANGLE 0.0f      // 目标角度
#define MAX_PWM_OUTPUT 800     // 最大PWM输出 (0-1000)
#define ANGLE_LIMIT 45.0f      // 角度保护限制

// 函数声明
void Balance_Control_Init(void);
float PID_Update(PID_Controller *pid, float error, float dt);
void Motor_SetSpeed(float speed);
void Balance_Control_Task(void);
void Emergency_Stop(void);

#endif
```

### 4.2 创建平衡控制实现 (Core/Src/balance_control.c)
```c
#include "balance_control.h"
#include "tim.h"
#include <stdio.h>

// PID控制器实例
static PID_Controller balance_pid;

// PID控制器初始化
void Balance_Control_Init(void) {
    // 直立环PD控制参数 (需要根据实际情况调节)
    balance_pid.Kp = 25.0f;    // 比例系数
    balance_pid.Ki = 0.0f;     // 积分系数 (暂时不用)
    balance_pid.Kd = 0.8f;     // 微分系数
    balance_pid.integral = 0.0f;
    balance_pid.prevError = 0.0f;
    balance_pid.integralLimit = 100.0f;
    balance_pid.outputLimit = MAX_PWM_OUTPUT;
}

// PID控制计算
float PID_Update(PID_Controller *pid, float error, float dt) {
    // 比例项
    float proportional = pid->Kp * error;
    
    // 积分项
    pid->integral += error * dt;
    if(pid->integral > pid->integralLimit) pid->integral = pid->integralLimit;
    else if(pid->integral < -pid->integralLimit) pid->integral = -pid->integralLimit;
    float integral = pid->Ki * pid->integral;
    
    // 微分项
    float derivative = pid->Kd * (error - pid->prevError) / dt;
    pid->prevError = error;
    
    // 总输出
    float output = proportional + integral + derivative;
    
    // 输出限幅
    if(output > pid->outputLimit) output = pid->outputLimit;
    else if(output < -pid->outputLimit) output = -pid->outputLimit;
    
    return output;
}

// 电机速度控制
void Motor_SetSpeed(float speed) {
    // 限制速度范围
    if(speed > MAX_PWM_OUTPUT) speed = MAX_PWM_OUTPUT;
    else if(speed < -MAX_PWM_OUTPUT) speed = -MAX_PWM_OUTPUT;
    
    if(speed >= 0) {
        // 正转
        HAL_GPIO_WritePin(AIN1_GPIO_Port, AIN1_Pin, GPIO_PIN_SET);
        HAL_GPIO_WritePin(AIN2_GPIO_Port, AIN2_Pin, GPIO_PIN_RESET);
        HAL_GPIO_WritePin(BIN1_GPIO_Port, BIN1_Pin, GPIO_PIN_SET);
        HAL_GPIO_WritePin(BIN2_GPIO_Port, BIN2_Pin, GPIO_PIN_RESET);
        
        // 设置PWM占空比
        __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, (uint32_t)speed);
        __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_2, (uint32_t)speed);
    } else {
        // 反转
        HAL_GPIO_WritePin(AIN1_GPIO_Port, AIN1_Pin, GPIO_PIN_RESET);
        HAL_GPIO_WritePin(AIN2_GPIO_Port, AIN2_Pin, GPIO_PIN_SET);
        HAL_GPIO_WritePin(BIN1_GPIO_Port, BIN1_Pin, GPIO_PIN_RESET);
        HAL_GPIO_WritePin(BIN2_GPIO_Port, BIN2_Pin, GPIO_PIN_SET);
        
        // 设置PWM占空比 (取绝对值)
        __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, (uint32_t)(-speed));
        __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_2, (uint32_t)(-speed));
    }
}

// 紧急停机
void Emergency_Stop(void) {
    // 停止所有电机
    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, 0);
    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_2, 0);
    
    // 设置所有方向引脚为低电平
    HAL_GPIO_WritePin(AIN1_GPIO_Port, AIN1_Pin, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(AIN2_GPIO_Port, AIN2_Pin, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(BIN1_GPIO_Port, BIN1_Pin, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(BIN2_GPIO_Port, BIN2_Pin, GPIO_PIN_RESET);
    
    printf("紧急停机！\r\n");
}

// 平衡控制主任务
void Balance_Control_Task(void) {
    static MPU6050_Data imu_data;
    static float current_angle;
    static float control_output;
    
    // 读取传感器数据
    if(MPU6050_ReadData(&imu_data) != HAL_OK) {
        printf("传感器读取失败！\r\n");
        Emergency_Stop();
        return;
    }
    
    // 计算当前角度
    current_angle = Calculate_Pitch_Angle(&imu_data);
    
    // 安全检查
    if(fabsf(current_angle) > ANGLE_LIMIT) {
        Emergency_Stop();
        return;
    }
    
    // PID控制计算
    float error = TARGET_ANGLE - current_angle;
    control_output = PID_Update(&balance_pid, error, SAMPLE_TIME);
    
    // 电机控制输出
    Motor_SetSpeed(control_output);
    
    // 调试信息输出 (每100ms输出一次)
    static uint32_t last_print_time = 0;
    if(HAL_GetTick() - last_print_time >= 100) {
        printf("角度: %.2f°, 误差: %.2f°, 输出: %.1f\r\n", 
               current_angle, error, control_output);
        last_print_time = HAL_GetTick();
    }
}
```

## 5. 第四步：修改主程序

### 5.1 修改main.c文件
在main.c中添加以下内容：

```c
// 在USER CODE BEGIN Includes部分添加
#include "mpu6050.h"
#include "attitude.h"
#include "balance_control.h"
#include <stdio.h>

// 在USER CODE BEGIN 2部分添加 (替换原有的电机测试代码)
// 初始化MPU6050
if(MPU6050_Init() != HAL_OK) {
    printf("MPU6050初始化失败！\r\n");
    Error_Handler();
}

// 初始化姿态解算
Attitude_Init();

// 初始化平衡控制
Balance_Control_Init();

// 启动PWM输出
HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_1);
HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_2);

printf("系统初始化完成，开始平衡控制...\r\n");

// 在USER CODE BEGIN 3部分添加 (主循环)
Balance_Control_Task();
HAL_Delay(5);  // 5ms控制周期
```

### 5.2 添加串口重定向 (可选)
```c
// 在main.c中添加printf重定向
#ifdef __GNUC__
#define PUTCHAR_PROTOTYPE int __io_putchar(int ch)
#else
#define PUTCHAR_PROTOTYPE int fputc(int ch, FILE *f)
#endif

PUTCHAR_PROTOTYPE {
    HAL_UART_Transmit(&huart1, (uint8_t *)&ch, 1, 0xFFFF);
    return ch;
}
```

## 6. 调试与测试步骤

### 6.1 第一阶段：验证传感器通信
1. 编译并下载程序
2. 打开串口调试助手 (115200波特率)
3. 检查是否输出"MPU6050初始化成功"
4. 观察传感器数据是否正常

### 6.2 第二阶段：验证姿态解算
1. 手动倾斜开发板
2. 观察角度计算是否准确
3. 检查角度变化是否平滑

### 6.3 第三阶段：验证平衡控制
1. 将开发板固定在平衡车上
2. 轻微推动车体
3. 观察是否能自动恢复平衡

### 6.4 参数调节指南
```c
// PID参数调节建议：
// 1. 先调Kp：从小到大，直到出现振荡
// 2. 再调Kd：增大Kd抑制振荡
// 3. 最后调Ki：消除静态误差 (通常很小或为0)

// 典型参数范围：
// Kp: 20-50
// Ki: 0-0.1  
// Kd: 0.5-2.0
```

## 7. 常见问题解决

### 7.1 I2C通信失败
- 检查硬件连接
- 确认上拉电阻 (4.7kΩ)
- 检查电源供电

### 7.2 角度计算不准确
- 校准传感器零点
- 调整滤波参数
- 检查传感器安装位置

### 7.3 控制不稳定
- 调整PID参数
- 检查控制周期
- 确认机械结构稳定

---
**实施指南完成，可以开始代码移植工作。**
