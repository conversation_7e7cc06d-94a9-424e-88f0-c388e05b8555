/**
 ******************************************************************************
 * @file    pid_controller.h
 * @brief   PID控制器头文件 - 支持位置式和增量式PID算法
 * <AUTHOR> (工程师)
 * @version v1.0
 * @date    2025-01-15
 ******************************************************************************
 * @attention
 * 
 * 本文件实现了完整的PID控制器功能，包括：
 * - 位置式PID算法
 * - 增量式PID算法  
 * - 参数限幅和保护机制
 * - 积分分离和微分先行
 * - 实时参数调节接口
 * 
 ******************************************************************************
 */

#ifndef __PID_CONTROLLER_H
#define __PID_CONTROLLER_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include <math.h>
#include <string.h>

/* Exported types ------------------------------------------------------------*/

/**
 * @brief PID控制器类型枚举
 */
typedef enum {
    PID_TYPE_POSITIONAL = 0,  /**< 位置式PID */
    PID_TYPE_INCREMENTAL = 1  /**< 增量式PID */
} PID_Type_t;

/**
 * @brief PID控制器状态枚举
 */
typedef enum {
    PID_STATE_DISABLED = 0,   /**< 禁用状态 */
    PID_STATE_ENABLED = 1,    /**< 启用状态 */
    PID_STATE_SATURATED = 2,  /**< 饱和状态 */
    PID_STATE_ERROR = 3       /**< 错误状态 */
} PID_State_t;

/**
 * @brief PID参数结构体
 */
typedef struct {
    float Kp;                 /**< 比例系数 */
    float Ki;                 /**< 积分系数 */
    float Kd;                 /**< 微分系数 */
    float max_output;         /**< 最大输出限制 */
    float min_output;         /**< 最小输出限制 */
    float max_integral;       /**< 积分限幅 */
    float deadzone;           /**< 死区范围 */
    float integral_separation_threshold; /**< 积分分离阈值 */
    uint8_t enable_integral_separation;  /**< 积分分离使能 */
    uint8_t enable_derivative_on_measurement; /**< 微分先行使能 */
} PID_Params_t;

/**
 * @brief PID内部状态结构体
 */
typedef struct {
    float error[3];           /**< 误差历史 [当前, 上次, 上上次] */
    float integral;           /**< 积分累积值 */
    float derivative;         /**< 微分值 */
    float last_output;        /**< 上次输出值 */
    float last_measurement;   /**< 上次测量值 (用于微分先行) */
    uint32_t last_time;       /**< 上次计算时间 (ms) */
    uint32_t update_count;    /**< 更新计数器 */
} PID_State_Data_t;

/**
 * @brief PID控制器主结构体
 */
typedef struct {
    PID_Type_t type;          /**< PID类型 */
    PID_State_t state;        /**< 控制器状态 */
    PID_Params_t params;      /**< PID参数 */
    PID_State_Data_t data;    /**< 内部状态数据 */
    float setpoint;           /**< 目标值 */
    float feedback;           /**< 反馈值 */
    float output;             /**< 输出值 */
    float dt;                 /**< 采样时间间隔 */
} PID_Controller_t;

/* Exported constants --------------------------------------------------------*/

/** @defgroup PID_Default_Parameters PID默认参数
 * @{
 */
#define PID_DEFAULT_KP              1.0f    /**< 默认比例系数 */
#define PID_DEFAULT_KI              0.0f    /**< 默认积分系数 */
#define PID_DEFAULT_KD              0.0f    /**< 默认微分系数 */
#define PID_DEFAULT_MAX_OUTPUT      1000.0f /**< 默认最大输出 */
#define PID_DEFAULT_MIN_OUTPUT      -1000.0f/**< 默认最小输出 */
#define PID_DEFAULT_MAX_INTEGRAL    500.0f  /**< 默认积分限幅 */
#define PID_DEFAULT_DEADZONE        0.1f    /**< 默认死区 */
#define PID_DEFAULT_INTEGRAL_SEP_THRESHOLD 10.0f /**< 默认积分分离阈值 */
/**
 * @}
 */

/** @defgroup PID_Error_Codes PID错误代码
 * @{
 */
#define PID_OK                      0       /**< 成功 */
#define PID_ERROR                   1       /**< 一般错误 */
#define PID_ERROR_NULL_POINTER      2       /**< 空指针错误 */
#define PID_ERROR_INVALID_PARAM     3       /**< 参数无效 */
#define PID_ERROR_NOT_INITIALIZED   4       /**< 未初始化 */
/**
 * @}
 */

/* Exported macro ------------------------------------------------------------*/

/**
 * @brief 限制数值在指定范围内
 */
#define PID_CONSTRAIN(value, min, max) \
    ((value) < (min) ? (min) : ((value) > (max) ? (max) : (value)))

/**
 * @brief 计算绝对值
 */
#define PID_ABS(x) ((x) > 0 ? (x) : -(x))

/**
 * @brief 检查PID控制器是否已初始化
 */
#define PID_IS_INITIALIZED(pid) ((pid) != NULL && (pid)->state != PID_STATE_ERROR)

/* Exported functions prototypes ---------------------------------------------*/

/** @defgroup PID_Initialization_Functions 初始化函数
 * @{
 */
HAL_StatusTypeDef PID_Init(PID_Controller_t *pid, PID_Type_t type);
HAL_StatusTypeDef PID_SetParams(PID_Controller_t *pid, const PID_Params_t *params);
HAL_StatusTypeDef PID_SetDefaultParams(PID_Controller_t *pid);
void PID_Reset(PID_Controller_t *pid);
/**
 * @}
 */

/** @defgroup PID_Control_Functions 控制函数
 * @{
 */
float PID_Update(PID_Controller_t *pid, float setpoint, float feedback, float dt);
float PID_UpdateWithTime(PID_Controller_t *pid, float setpoint, float feedback);
void PID_Enable(PID_Controller_t *pid);
void PID_Disable(PID_Controller_t *pid);
/**
 * @}
 */

/** @defgroup PID_Parameter_Functions 参数调节函数
 * @{
 */
void PID_SetKp(PID_Controller_t *pid, float kp);
void PID_SetKi(PID_Controller_t *pid, float ki);
void PID_SetKd(PID_Controller_t *pid, float kd);
void PID_SetOutputLimits(PID_Controller_t *pid, float min, float max);
void PID_SetIntegralLimit(PID_Controller_t *pid, float max_integral);
void PID_SetDeadzone(PID_Controller_t *pid, float deadzone);
void PID_SetIntegralSeparation(PID_Controller_t *pid, uint8_t enable, float threshold);
void PID_SetDerivativeOnMeasurement(PID_Controller_t *pid, uint8_t enable);
/**
 * @}
 */

/** @defgroup PID_Status_Functions 状态查询函数
 * @{
 */
PID_State_t PID_GetState(const PID_Controller_t *pid);
float PID_GetError(const PID_Controller_t *pid);
float PID_GetIntegral(const PID_Controller_t *pid);
float PID_GetDerivative(const PID_Controller_t *pid);
float PID_GetOutput(const PID_Controller_t *pid);
uint32_t PID_GetUpdateCount(const PID_Controller_t *pid);
/**
 * @}
 */

/** @defgroup PID_Utility_Functions 工具函数
 * @{
 */
void PID_PrintStatus(const PID_Controller_t *pid);
HAL_StatusTypeDef PID_SelfTest(PID_Controller_t *pid);
/**
 * @}
 */

#ifdef __cplusplus
}
#endif

#endif /* __PID_CONTROLLER_H */

/**
 * @}
 */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
