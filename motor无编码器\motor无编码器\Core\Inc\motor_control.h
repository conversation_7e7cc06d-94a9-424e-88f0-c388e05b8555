/**
 ******************************************************************************
 * @file    motor_control.h
 * @brief   电机控制器头文件 - TB6612FNG双电机驱动控制
 * <AUTHOR> (工程师)
 * @version v1.0
 * @date    2025-01-15
 ******************************************************************************
 * @attention
 * 
 * 本文件实现了TB6612FNG双电机驱动的控制功能，包括：
 * - PWM速度控制
 * - 方向控制
 * - 安全保护机制
 * - 软启动功能
 * - 状态监控
 * 
 ******************************************************************************
 */

#ifndef __MOTOR_CONTROL_H
#define __MOTOR_CONTROL_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "tim.h"
#include "gpio.h"
#include <math.h>

/* Exported types ------------------------------------------------------------*/

/**
 * @brief 电机方向枚举
 */
typedef enum {
    MOTOR_DIR_STOP = 0,      /**< 停止 */
    MOTOR_DIR_FORWARD = 1,   /**< 前进 */
    MOTOR_DIR_BACKWARD = 2   /**< 后退 */
} Motor_Direction_t;

/**
 * @brief 电机状态枚举
 */
typedef enum {
    MOTOR_STATE_DISABLED = 0,  /**< 禁用状态 */
    MOTOR_STATE_ENABLED = 1,   /**< 启用状态 */
    MOTOR_STATE_FAULT = 2,     /**< 故障状态 */
    MOTOR_STATE_EMERGENCY = 3  /**< 紧急停止状态 */
} Motor_State_t;

/**
 * @brief 电机ID枚举
 */
typedef enum {
    MOTOR_LEFT = 0,    /**< 左电机 */
    MOTOR_RIGHT = 1,   /**< 右电机 */
    MOTOR_BOTH = 2     /**< 双电机 */
} Motor_ID_t;

/**
 * @brief 电机配置结构体
 */
typedef struct {
    uint16_t max_pwm_value;      /**< 最大PWM值 */
    uint16_t min_pwm_value;      /**< 最小PWM值 */
    uint16_t deadzone_pwm;       /**< PWM死区 */
    float speed_limit;           /**< 速度限制 (-1.0 ~ 1.0) */
    uint8_t enable_soft_start;   /**< 软启动使能 */
    uint16_t soft_start_time;    /**< 软启动时间 (ms) */
    uint8_t enable_brake;        /**< 刹车使能 */
    uint16_t brake_time;         /**< 刹车时间 (ms) */
} Motor_Config_t;

/**
 * @brief 单个电机状态结构体
 */
typedef struct {
    Motor_Direction_t direction; /**< 当前方向 */
    uint16_t pwm_value;         /**< PWM值 */
    float speed_command;        /**< 速度命令 (-1.0 ~ 1.0) */
    float actual_speed;         /**< 实际速度 */
    uint8_t is_enabled;         /**< 使能状态 */
    uint32_t fault_count;       /**< 故障计数 */
    uint32_t run_time;          /**< 运行时间 (ms) */
} Motor_Status_t;

/**
 * @brief 电机控制器主结构体
 */
typedef struct {
    Motor_State_t state;         /**< 控制器状态 */
    Motor_Config_t config;       /**< 配置参数 */
    
    Motor_Status_t left_motor;   /**< 左电机状态 */
    Motor_Status_t right_motor;  /**< 右电机状态 */
    
    TIM_HandleTypeDef *htim;     /**< 定时器句柄 */
    uint32_t pwm_channel_left;   /**< 左电机PWM通道 */
    uint32_t pwm_channel_right;  /**< 右电机PWM通道 */
    
    uint32_t last_update_time;   /**< 上次更新时间 */
    uint32_t total_run_time;     /**< 总运行时间 */
    uint32_t emergency_count;    /**< 紧急停止计数 */
} Motor_Controller_t;

/* Exported constants --------------------------------------------------------*/

/** @defgroup Motor_Default_Config 默认配置参数
 * @{
 */
#define MOTOR_DEFAULT_MAX_PWM           1000    /**< 默认最大PWM值 */
#define MOTOR_DEFAULT_MIN_PWM           0       /**< 默认最小PWM值 */
#define MOTOR_DEFAULT_DEADZONE_PWM      50      /**< 默认PWM死区 */
#define MOTOR_DEFAULT_SPEED_LIMIT       1.0f    /**< 默认速度限制 */
#define MOTOR_DEFAULT_SOFT_START_TIME   500     /**< 默认软启动时间 */
#define MOTOR_DEFAULT_BRAKE_TIME        200     /**< 默认刹车时间 */
/**
 * @}
 */

/** @defgroup Motor_PWM_Channels PWM通道定义
 * @{
 */
#define MOTOR_PWM_CHANNEL_LEFT          TIM_CHANNEL_1  /**< 左电机PWM通道 (PE9) */
#define MOTOR_PWM_CHANNEL_RIGHT         TIM_CHANNEL_2  /**< 右电机PWM通道 (PE11) */
/**
 * @}
 */

/** @defgroup Motor_Error_Codes 错误代码
 * @{
 */
#define MOTOR_ERROR_NONE                0       /**< 无错误 */
#define MOTOR_ERROR_PWM_FAULT           1       /**< PWM故障 */
#define MOTOR_ERROR_OVERCURRENT         2       /**< 过流保护 */
#define MOTOR_ERROR_OVERTEMPERATURE     3       /**< 过温保护 */
#define MOTOR_ERROR_INVALID_PARAMETER   4       /**< 参数无效 */
#define MOTOR_ERROR_TIMEOUT             5       /**< 超时错误 */
/**
 * @}
 */

/* Exported macro ------------------------------------------------------------*/

/**
 * @brief 速度值限制在有效范围内
 */
#define MOTOR_CONSTRAIN_SPEED(speed) \
    ((speed) < -1.0f ? -1.0f : ((speed) > 1.0f ? 1.0f : (speed)))

/**
 * @brief PWM值限制在有效范围内
 */
#define MOTOR_CONSTRAIN_PWM(pwm, max_pwm) \
    ((pwm) > (max_pwm) ? (max_pwm) : (pwm))

/**
 * @brief 检查电机控制器是否就绪
 */
#define MOTOR_IS_READY(motor) \
    ((motor) != NULL && (motor)->state == MOTOR_STATE_ENABLED)

/**
 * @brief 速度转PWM值
 */
#define MOTOR_SPEED_TO_PWM(speed, max_pwm) \
    ((uint16_t)(fabsf(speed) * (max_pwm)))

/* Exported functions prototypes ---------------------------------------------*/

/** @defgroup Motor_Initialization_Functions 初始化函数
 * @{
 */
HAL_StatusTypeDef Motor_Init(Motor_Controller_t *motor, TIM_HandleTypeDef *htim);
HAL_StatusTypeDef Motor_SetConfig(Motor_Controller_t *motor, const Motor_Config_t *config);
HAL_StatusTypeDef Motor_SetDefaultConfig(Motor_Controller_t *motor);
void Motor_Reset(Motor_Controller_t *motor);
/**
 * @}
 */

/** @defgroup Motor_Control_Functions 控制函数
 * @{
 */
HAL_StatusTypeDef Motor_SetSpeed(Motor_Controller_t *motor, float speed);
HAL_StatusTypeDef Motor_SetSpeedDifferential(Motor_Controller_t *motor, float left_speed, float right_speed);
HAL_StatusTypeDef Motor_SetDirection(Motor_Controller_t *motor, Motor_Direction_t direction);
HAL_StatusTypeDef Motor_SetPWM(Motor_Controller_t *motor, Motor_ID_t motor_id, uint16_t pwm_value);
void Motor_Stop(Motor_Controller_t *motor);
void Motor_EmergencyStop(Motor_Controller_t *motor);
void Motor_Brake(Motor_Controller_t *motor);
/**
 * @}
 */

/** @defgroup Motor_Enable_Functions 使能函数
 * @{
 */
void Motor_Enable(Motor_Controller_t *motor);
void Motor_Disable(Motor_Controller_t *motor);
HAL_StatusTypeDef Motor_StartPWM(Motor_Controller_t *motor);
HAL_StatusTypeDef Motor_StopPWM(Motor_Controller_t *motor);
/**
 * @}
 */

/** @defgroup Motor_Status_Functions 状态查询函数
 * @{
 */
Motor_State_t Motor_GetState(const Motor_Controller_t *motor);
Motor_Status_t* Motor_GetMotorStatus(Motor_Controller_t *motor, Motor_ID_t motor_id);
uint8_t Motor_IsEnabled(const Motor_Controller_t *motor);
uint16_t Motor_GetPWMValue(const Motor_Controller_t *motor, Motor_ID_t motor_id);
float Motor_GetSpeed(const Motor_Controller_t *motor, Motor_ID_t motor_id);
Motor_Direction_t Motor_GetDirection(const Motor_Controller_t *motor, Motor_ID_t motor_id);
/**
 * @}
 */

/** @defgroup Motor_Utility_Functions 工具函数
 * @{
 */
void Motor_PrintStatus(const Motor_Controller_t *motor);
HAL_StatusTypeDef Motor_SelfTest(Motor_Controller_t *motor);
const char* Motor_GetStateString(Motor_State_t state);
const char* Motor_GetDirectionString(Motor_Direction_t direction);
/**
 * @}
 */

/** @defgroup Motor_Advanced_Functions 高级功能函数
 * @{
 */
HAL_StatusTypeDef Motor_SoftStart(Motor_Controller_t *motor, float target_speed);
HAL_StatusTypeDef Motor_SoftStop(Motor_Controller_t *motor);
void Motor_UpdateStatus(Motor_Controller_t *motor);
/**
 * @}
 */

#ifdef __cplusplus
}
#endif

#endif /* __MOTOR_CONTROL_H */

/**
 * @}
 */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
