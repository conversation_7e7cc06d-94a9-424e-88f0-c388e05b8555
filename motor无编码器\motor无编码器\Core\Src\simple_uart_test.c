/**
 ******************************************************************************
 * @file    simple_uart_test.c
 * @brief   简单的串口测试程序
 * <AUTHOR> (工程师)
 * @version v1.0
 * @date    2025-01-15
 ******************************************************************************
 * @attention
 * 
 * 这是一个最简单的串口测试程序，用于验证串口是否正常工作
 * 如果主程序无法输出，可以用这个程序替换main函数进行测试
 * 
 ******************************************************************************
 */

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "usart.h"
#include "gpio.h"
#include <stdio.h>

/* Private variables ---------------------------------------------------------*/

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);

/**
 * @brief  简单的串口测试主函数
 * @retval int
 */
int simple_uart_test_main(void)
{
  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* Configure the system clock */
  SystemClock_Config();

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_USART1_UART_Init();
  MX_USART2_UART_Init();

  /* 简单的串口测试 */
  printf("=== Simple UART Test ===\r\n");
  printf("Testing USART2 (PD5/PD6)...\r\n");
  printf("If you can see this message, UART is working!\r\n");
  printf("STM32F407 System Clock: %lu Hz\r\n", HAL_RCC_GetSysClockFreq());
  printf("========================\r\n\r\n");

  uint32_t counter = 0;
  uint32_t last_time = HAL_GetTick();

  /* Infinite loop */
  while (1)
  {
    uint32_t current_time = HAL_GetTick();
    
    // 每1秒输出一次
    if (current_time - last_time >= 1000) {
      printf("UART Test %lu: System running OK, Tick = %lu\r\n", 
             ++counter, current_time);
      last_time = current_time;
      
      // 闪烁LED (如果有的话)
      HAL_GPIO_TogglePin(GPIOD, GPIO_PIN_12);  // 尝试闪烁PD12 LED
      HAL_GPIO_TogglePin(GPIOD, GPIO_PIN_13);  // 尝试闪烁PD13 LED
      HAL_GPIO_TogglePin(GPIOD, GPIO_PIN_14);  // 尝试闪烁PD14 LED
      HAL_GPIO_TogglePin(GPIOD, GPIO_PIN_15);  // 尝试闪烁PD15 LED
    }
    
    // 测试不同的串口输出
    if (counter % 5 == 0 && current_time - last_time >= 500) {
      // 每5秒测试一次USART1
      HAL_UART_Transmit(&huart1, (uint8_t*)"USART1 Test\r\n", 13, 1000);
    }
  }
}

/**
 * @brief  printf重定向到USART2
 */
#ifdef __GNUC__
#define PUTCHAR_PROTOTYPE int __io_putchar(int ch)
#else
#define PUTCHAR_PROTOTYPE int fputc(int ch, FILE *f)
#endif

PUTCHAR_PROTOTYPE {
    HAL_UART_Transmit(&huart2, (uint8_t *)&ch, 1, 0xFFFF);
    return ch;
}

/**
 * @brief  直接串口发送函数 (不依赖printf)
 * @param  str: 要发送的字符串
 * @retval None
 */
void UART_SendString(const char* str)
{
    HAL_UART_Transmit(&huart2, (uint8_t*)str, strlen(str), 1000);
}

/**
 * @brief  测试所有可能的串口配置
 * @retval None
 */
void Test_All_UART_Configs(void)
{
    // 测试USART2 (PD5/PD6)
    printf("Testing USART2 (PD5/PD6) with printf...\r\n");
    HAL_Delay(100);
    
    // 测试USART2直接发送
    UART_SendString("Testing USART2 (PD5/PD6) with direct HAL...\r\n");
    HAL_Delay(100);
    
    // 测试USART1 (PA9/PA10)
    HAL_UART_Transmit(&huart1, (uint8_t*)"Testing USART1 (PA9/PA10) with direct HAL...\r\n", 47, 1000);
    HAL_Delay(100);
    
    printf("All UART tests completed!\r\n");
}

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
