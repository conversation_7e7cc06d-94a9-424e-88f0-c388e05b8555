# PID参数调节指南

## 1. 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-01-15
- **负责人**: <PERSON> (产品经理) + <PERSON> (工程师)
- **项目名称**: STM32F4平衡车PID参数调节完整指南
- **最后更新**: 2025-01-15

## 2. PID控制理论基础

### 2.1 PID控制器原理
PID控制器是一种经典的反馈控制器，由三个部分组成：

**位置式PID公式**：
```
output = Kp × error + Ki × ∫error×dt + Kd × d(error)/dt
```

**增量式PID公式**：
```
Δoutput = Kp×(error[k]-error[k-1]) + Ki×error[k] + Kd×(error[k]-2×error[k-1]+error[k-2])
```

### 2.2 各参数作用详解

#### 2.2.1 比例项 (Kp)
- **作用**: 根据当前误差大小产生控制量
- **特点**: 响应快速，但存在稳态误差
- **调节效果**:
  - Kp过小：响应慢，稳态误差大
  - Kp过大：超调大，可能振荡
  - Kp适中：响应快，超调小

#### 2.2.2 积分项 (Ki)
- **作用**: 消除稳态误差
- **特点**: 对历史误差进行累积
- **调节效果**:
  - Ki过小：稳态误差消除慢
  - Ki过大：超调大，调节时间长
  - Ki适中：快速消除稳态误差

#### 2.2.3 微分项 (Kd)
- **作用**: 预测误差变化趋势，提前调节
- **特点**: 对噪声敏感
- **调节效果**:
  - Kd过小：超调大，振荡
  - Kd过大：对噪声敏感，响应慢
  - Kd适中：减少超调，提高稳定性

## 3. 平衡车PID调节策略

### 3.1 系统特性分析
平衡车是一个**不稳定系统**，具有以下特点：
- 开环不稳定，需要闭环控制
- 响应速度要求高 (≤50ms)
- 对参数敏感，需要精确调节
- 存在机械延迟和传感器噪声

### 3.2 调节优先级
1. **Kp (比例项)** - 最重要，决定基本响应
2. **Kd (微分项)** - 次重要，提供阻尼
3. **Ki (积分项)** - 最后调节，消除稳态误差

### 3.3 推荐参数范围
基于STM32F4平衡车系统的经验值：

| 参数 | 最小值 | 推荐起始值 | 最大值 | 备注 |
|------|--------|------------|--------|------|
| Kp   | 5.0    | 15.0       | 30.0   | 主要参数 |
| Ki   | 0.0    | 0.5        | 2.0    | 谨慎调节 |
| Kd   | 0.0    | 0.8        | 3.0    | 抗噪声 |

## 4. 手动调节步骤

### 4.1 准备工作
1. **硬件检查**
   - 确认MPU6050正常工作
   - 检查电机和驱动电路
   - 验证机械结构稳固

2. **软件准备**
   - 编译并下载程序
   - 连接串口调试工具
   - 准备参数调节界面

3. **环境准备**
   - 选择平整的测试场地
   - 确保充足的测试空间
   - 准备支撑工具（初期测试）

### 4.2 Step 1: 确定基础Kp值
```c
// 设置初始参数
Balance_System_SetPIDParams(&g_balance_system, 10.0f, 0.0f, 0.0f);
```

**调节过程**：
1. 从Kp=5开始测试
2. 逐步增加Kp值 (每次+2)
3. 观察系统响应：
   - Kp太小：倾倒无法自立
   - Kp合适：能够短暂保持平衡
   - Kp太大：剧烈振荡

**目标**：找到能够短暂平衡的最小Kp值

### 4.3 Step 2: 添加微分项Kd
```c
// 在确定Kp基础上添加Kd
Balance_System_SetPIDParams(&g_balance_system, 15.0f, 0.0f, 0.5f);
```

**调节过程**：
1. 从Kd=0.2开始
2. 逐步增加Kd值 (每次+0.2)
3. 观察振荡改善情况：
   - Kd太小：仍有振荡
   - Kd合适：振荡明显减少
   - Kd太大：响应变慢

**目标**：获得稳定的平衡状态

### 4.4 Step 3: 微调积分项Ki
```c
// 最后添加少量Ki
Balance_System_SetPIDParams(&g_balance_system, 15.0f, 0.3f, 0.8f);
```

**调节过程**：
1. 从Ki=0.1开始
2. 小幅增加Ki值 (每次+0.1)
3. 观察稳态误差：
   - Ki太小：存在稳态偏差
   - Ki合适：无稳态误差，稳定
   - Ki太大：超调增大

**目标**：消除稳态误差，保持稳定

## 5. 自动调节方法

### 5.1 Ziegler-Nichols方法
系统提供了自动调节功能：

```c
// 启动自动调节
Balance_System_TunePIDParams(&g_balance_system);
```

**调节原理**：
1. 测试不同Kp值的系统响应
2. 计算平均误差作为性能指标
3. 选择性能最佳的参数组合

### 5.2 遗传算法优化 (高级)
```c
// 多参数全局优化 (需要额外实现)
typedef struct {
    float kp, ki, kd;
    float fitness;  // 适应度
} PID_Individual_t;

// 遗传算法参数优化
HAL_StatusTypeDef PID_GeneticOptimization(PID_Individual_t *best_params);
```

## 6. 调节技巧与经验

### 6.1 常见问题及解决方案

#### 问题1：系统无法启动平衡
**现象**：车体立即倾倒
**原因**：Kp值过小
**解决**：增大Kp值至15-20

#### 问题2：剧烈振荡
**现象**：车体左右摆动
**原因**：Kp过大或Kd不足
**解决**：减小Kp或增大Kd

#### 问题3：缓慢振荡
**现象**：低频摆动
**原因**：Ki过大
**解决**：减小Ki值

#### 问题4：稳态偏差
**现象**：平衡但有角度偏移
**原因**：Ki不足或零点偏移
**解决**：增加Ki或重新校准

### 6.2 高级调节技巧

#### 6.2.1 多级控制策略
```c
// 根据误差大小使用不同参数
if (fabsf(angle_error) > 10.0f) {
    // 大误差：快速响应
    PID_SetParams(&pid, 25.0f, 0.2f, 1.5f);
} else if (fabsf(angle_error) > 2.0f) {
    // 中误差：平衡响应
    PID_SetParams(&pid, 15.0f, 0.5f, 0.8f);
} else {
    // 小误差：精确控制
    PID_SetParams(&pid, 12.0f, 0.8f, 0.5f);
}
```

#### 6.2.2 积分分离
```c
// 大误差时禁用积分项
if (fabsf(angle_error) > 5.0f) {
    PID_SetIntegralSeparation(&pid, 1, 5.0f);
}
```

#### 6.2.3 微分先行
```c
// 对测量值而非误差进行微分
PID_SetDerivativeOnMeasurement(&pid, 1);
```

## 7. 性能评估指标

### 7.1 定量指标
- **平衡精度**：稳态误差 ≤ ±2°
- **响应时间**：从倾斜到稳定 ≤ 1秒
- **稳定时间**：连续平衡 ≥ 30秒
- **超调量**：初始响应超调 ≤ 10°

### 7.2 定性指标
- **稳定性**：无持续振荡
- **鲁棒性**：抗干扰能力强
- **平滑性**：动作流畅自然

## 8. 调节记录模板

### 8.1 参数记录表
| 测试序号 | Kp   | Ki   | Kd   | 平衡时间(s) | 振荡情况 | 备注 |
|----------|------|------|------|-------------|----------|------|
| 1        | 10.0 | 0.0  | 0.0  | 0           | 倾倒     | Kp太小 |
| 2        | 15.0 | 0.0  | 0.0  | 3           | 轻微振荡 | 基础值 |
| 3        | 15.0 | 0.0  | 0.5  | 8           | 稳定     | 添加Kd |
| 4        | 15.0 | 0.3  | 0.5  | 15          | 很稳定   | 最终值 |

### 8.2 测试环境记录
- **测试日期**：2025-01-15
- **环境温度**：25°C
- **电池电压**：12.0V
- **测试场地**：室内平整地面
- **车体重量**：1.2kg

## 9. 故障排除

### 9.1 硬件问题
- **传感器故障**：检查I2C通信
- **电机问题**：检查PWM输出和方向控制
- **电源问题**：确认电压稳定

### 9.2 软件问题
- **参数越界**：检查参数范围限制
- **计算溢出**：检查数据类型和范围
- **时序问题**：确认控制频率

### 9.3 机械问题
- **重心偏移**：调整重心位置
- **摩擦过大**：检查轴承和传动
- **结构松动**：紧固所有连接

## 10. 进阶优化

### 10.1 自适应PID
根据系统状态动态调整参数：
```c
void Adaptive_PID_Update(float angle, float angular_velocity) {
    if (fabsf(angular_velocity) > 50.0f) {
        // 快速运动时增加阻尼
        current_kd *= 1.2f;
    }
    
    if (fabsf(angle) < 1.0f) {
        // 接近平衡时提高精度
        current_ki *= 1.1f;
    }
}
```

### 10.2 模糊PID控制
结合模糊逻辑的PID参数调节：
```c
typedef struct {
    float error_fuzzy;
    float error_rate_fuzzy;
    float kp_adjust;
    float ki_adjust;
    float kd_adjust;
} Fuzzy_PID_t;
```

---

**调节成功标准**：
- 车体能够稳定直立≥30秒
- 受到轻微推力后能快速恢复平衡
- 无明显振荡和稳态误差
- 动作平滑自然

**安全提醒**：
- 调节过程中注意安全，避免车体撞击
- 参数调节应循序渐进，避免大幅跳跃
- 保存有效参数组合，便于回退
