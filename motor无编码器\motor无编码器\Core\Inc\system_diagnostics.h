/**
 ******************************************************************************
 * @file    system_diagnostics.h
 * @brief   系统诊断工具头文件
 * <AUTHOR> (工程师)
 * @version v1.0
 * @date    2025-01-15
 ******************************************************************************
 * @attention
 * 
 * 本文件实现了系统诊断功能，用于检查和调试系统各个模块的工作状态
 * 
 ******************************************************************************
 */

#ifndef __SYSTEM_DIAGNOSTICS_H
#define __SYSTEM_DIAGNOSTICS_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "mpu6050.h"
#include "motor_control.h"
#include "balance_system.h"
#include <stdio.h>

/* Exported types ------------------------------------------------------------*/

/**
 * @brief 诊断结果枚举
 */
typedef enum {
    DIAG_RESULT_PASS = 0,     /**< 测试通过 */
    DIAG_RESULT_FAIL = 1,     /**< 测试失败 */
    DIAG_RESULT_WARNING = 2   /**< 测试警告 */
} Diag_Result_t;

/**
 * @brief 诊断项目结构体
 */
typedef struct {
    const char *name;         /**< 测试项目名称 */
    Diag_Result_t result;     /**< 测试结果 */
    const char *message;      /**< 结果消息 */
} Diag_Item_t;

/**
 * @brief 系统诊断结构体
 */
typedef struct {
    uint8_t total_tests;      /**< 总测试数 */
    uint8_t passed_tests;     /**< 通过测试数 */
    uint8_t failed_tests;     /**< 失败测试数 */
    uint8_t warning_tests;    /**< 警告测试数 */
    Diag_Item_t items[20];    /**< 诊断项目数组 */
} System_Diagnostics_t;

/* Exported constants --------------------------------------------------------*/

/* Exported macro ------------------------------------------------------------*/

/* Exported functions prototypes ---------------------------------------------*/

/** @defgroup Diagnostics_Main_Functions 主要诊断函数
 * @{
 */
void System_Diagnostics_Init(System_Diagnostics_t *diag);
void System_Diagnostics_RunAll(System_Diagnostics_t *diag);
void System_Diagnostics_PrintReport(const System_Diagnostics_t *diag);
/**
 * @}
 */

/** @defgroup Diagnostics_Individual_Tests 单项测试函数
 * @{
 */
Diag_Result_t System_Diagnostics_TestClock(void);
Diag_Result_t System_Diagnostics_TestGPIO(void);
Diag_Result_t System_Diagnostics_TestI2C(void);
Diag_Result_t System_Diagnostics_TestMPU6050(void);
Diag_Result_t System_Diagnostics_TestPWM(void);
Diag_Result_t System_Diagnostics_TestMotor(void);
Diag_Result_t System_Diagnostics_TestUART(void);
Diag_Result_t System_Diagnostics_TestMemory(void);
/**
 * @}
 */

/** @defgroup Diagnostics_Utility_Functions 工具函数
 * @{
 */
void System_Diagnostics_AddResult(System_Diagnostics_t *diag, const char *name, 
                                  Diag_Result_t result, const char *message);
const char* System_Diagnostics_GetResultString(Diag_Result_t result);
/**
 * @}
 */

#ifdef __cplusplus
}
#endif

#endif /* __SYSTEM_DIAGNOSTICS_H */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
