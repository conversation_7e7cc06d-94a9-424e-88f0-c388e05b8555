# 🔌 STM32F4 PID平衡车控制系统 - 串口连接指南

## 📋 当前串口配置

**程序已修改为使用USART2输出**  
**修改时间**: 2025-01-15  
**状态**: ✅ **已切换到USART2**  

---

## 🔌 硬件连接方案

### 方案A: USART2连接 (当前使用 - 推荐)
```
STM32F407    →    USB转串口模块
PD5 (TX)     →    RX
PD6 (RX)     →    TX
GND          →    GND
3.3V         →    VCC (如果USB转串口模块需要外部供电)
```

### 方案B: USART1连接 (备选)
```
STM32F407    →    USB转串口模块  
PA9 (TX)     →    RX
PA10 (RX)    →    TX
GND          →    GND
3.3V         →    VCC (如果USB转串口模块需要外部供电)
```

---

## 🔍 引脚位置确认

### STM32F407VET6 (100引脚封装)
```
PD5 (USART2_TX): 第59引脚
PD6 (USART2_RX): 第60引脚
PA9 (USART1_TX): 第68引脚  
PA10(USART1_RX): 第69引脚
```

### STM32F407ZGT6 (144引脚封装)
```
PD5 (USART2_TX): 第86引脚
PD6 (USART2_RX): 第87引脚
PA9 (USART1_TX): 第101引脚
PA10(USART1_RX): 第102引脚
```

---

## 🛠️ 常见开发板连接方式

### 正点原子探索者F407
```
板载串口: USART1 (PA9/PA10)
连接方式: 直接用USB线连接到电脑
注意: 需要安装CH340驱动
```

### 野火F407开发板
```
板载串口: USART2 (PA2/PA3) 或 USART1 (PA9/PA10)
连接方式: 查看开发板丝印标注
注意: 可能需要跳线帽设置
```

### STM32F407VET6黑色开发板
```
板载串口: 通常没有板载USB转串口
连接方式: 需要外接USB转串口模块
推荐使用: USART2 (PD5/PD6)
```

---

## 📱 串口助手设置

### 基本设置
```
波特率: 115200
数据位: 8
停止位: 1
校验位: None (无校验)
流控制: None (无流控)
```

### 高级设置
```
接收设置: 
- ✅ 显示接收数据
- ✅ 自动换行
- ✅ 显示时间戳 (可选)

发送设置:
- ✅ 发送新行 (勾选)
- ✅ 十六进制显示 (可选)
```

---

## 🚀 测试步骤

### Step 1: 硬件连接 (5分钟)
1. **断开STM32电源**
2. **按照方案A连接USART2**:
   - PD5 → USB转串口的RX
   - PD6 → USB转串口的TX  
   - GND → GND
3. **连接USB转串口到电脑**
4. **给STM32上电**

### Step 2: 确认COM口 (2分钟)
1. 打开设备管理器
2. 查看"端口(COM和LPT)"
3. 找到USB转串口设备 (如CH340, CP2102, FT232等)
4. 记住COM口号 (如COM3, COM4等)

### Step 3: 打开串口助手 (1分钟)
1. 选择正确的COM口
2. 设置波特率115200
3. 点击"打开串口"

### Step 4: 重新编译下载 (3分钟)
1. 在Keil中重新编译 (F7)
2. 下载程序 (F8)
3. 观察串口输出

---

## 📊 预期输出

### 正常启动输出
```
UART Test: Hello World!
UART Test: STM32F407 Ready!
UART Test: Using USART2 (PD5/PD6)

=== PID Balance Car System Starting ===
Initializing complete balance control system...

=== Running System Diagnostics ===
Testing system clock...
  SYSCLK: 84000000 Hz
  ✅ Clock frequencies OK

Testing GPIO configuration...
  ✅ Motor direction pins OK

Testing UART communication...
  ✅ UART communication OK
...
```

### 如果仍无输出
程序会每秒输出一次简单测试信息：
```
UART Test 1: System running OK, Tick = 1000
UART Test 2: System running OK, Tick = 2000
UART Test 3: System running OK, Tick = 3000
...
```

---

## 🔧 故障排除

### 问题1: 仍然没有任何输出
**可能原因**:
1. 硬件连接错误
2. COM口选择错误
3. USB转串口驱动问题
4. 程序未正常启动

**解决方案**:
```
1. 检查连接: PD5→RX, PD6→TX, GND→GND
2. 重新选择COM口
3. 重新安装USB转串口驱动
4. 检查STM32是否正常上电和运行
```

### 问题2: 输出乱码
**可能原因**:
1. 波特率设置错误
2. 数据位/停止位设置错误
3. 时钟配置问题

**解决方案**:
```
1. 确认波特率设置为115200
2. 确认数据位8，停止位1，无校验
3. 检查系统时钟配置
```

### 问题3: 输出不完整或断断续续
**可能原因**:
1. 连接不稳定
2. 供电不足
3. 干扰问题

**解决方案**:
```
1. 检查连接是否牢固
2. 确保STM32供电稳定
3. 远离干扰源，使用屏蔽线
```

---

## 🆘 紧急备用方案

### 方案1: 切换回USART1
如果USART2仍然无效，我可以为您切换回USART1：
```c
// 修改printf重定向
HAL_UART_Transmit(&huart1, (uint8_t *)&ch, 1, 0xFFFF);

// 连接PA9→RX, PA10→TX
```

### 方案2: 使用最简单的测试程序
我已经为您准备了`simple_uart_test.c`，可以替换main函数进行最基本的串口测试。

### 方案3: LED指示灯调试
如果串口完全无法工作，可以使用LED闪烁来确认程序是否正常运行：
```c
// 在主循环中添加
HAL_GPIO_TogglePin(GPIOD, GPIO_PIN_12);  // 闪烁LED
HAL_Delay(500);
```

---

## 📞 技术支持

### 常用USB转串口芯片驱动下载
- **CH340/CH341**: [沁恒官网](http://www.wch.cn)
- **CP2102/CP2104**: [Silicon Labs官网](https://www.silabs.com)
- **FT232**: [FTDI官网](https://www.ftdichip.com)

### 推荐串口调试工具
- **SSCOM**: 简单易用的串口助手
- **PuTTY**: 功能强大的终端工具
- **Tera Term**: 专业的串口终端
- **串口调试助手**: Windows自带工具

---

## 🏆 成功标志

当您看到以下输出时，说明串口工作正常：

✅ **看到启动测试信息** - UART Test: Hello World!  
✅ **看到系统启动信息** - PID Balance Car System Starting  
✅ **看到诊断报告** - System Diagnostics  
✅ **能发送串口命令** - 输入help有响应  

**现在请按照指南重新连接硬件并测试！** 🚀

---

**连接工程师**: Alex (工程师)  
**指南版本**: v1.0  
**更新时间**: 2025-01-15
