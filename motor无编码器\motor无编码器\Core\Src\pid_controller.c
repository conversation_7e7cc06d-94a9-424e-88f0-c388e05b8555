/**
 ******************************************************************************
 * @file    pid_controller.c
 * @brief   PID控制器实现文件 - 支持位置式和增量式PID算法
 * <AUTHOR> (工程师)
 * @version v1.0
 * @date    2025-01-15
 ******************************************************************************
 */

/* Includes ------------------------------------------------------------------*/
#include "pid_controller.h"
#include <stdio.h>
#include <math.h>
#include <string.h>

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/
static float PID_CalculatePositional(PID_Controller_t *pid);
static float PID_CalculateIncremental(PID_Controller_t *pid);
static void PID_UpdateErrorHistory(PID_Controller_t *pid, float error);
static uint8_t PID_ValidateParams(const PID_Params_t *params);

/* Exported functions --------------------------------------------------------*/

/**
 * @brief  初始化PID控制器
 * @param  pid: PID控制器指针
 * @param  type: PID类型 (位置式/增量式)
 * @retval HAL状态
 */
HAL_StatusTypeDef PID_Init(PID_Controller_t *pid, PID_Type_t type)
{
    // 参数检查
    if (pid == NULL) {
        return HAL_ERROR;
    }
    
    // 清零结构体
    memset(pid, 0, sizeof(PID_Controller_t));
    
    // 设置PID类型
    pid->type = type;
    pid->state = PID_STATE_DISABLED;
    
    // 设置默认参数
    PID_SetDefaultParams(pid);
    
    // 重置内部状态
    PID_Reset(pid);
    
    pid->state = PID_STATE_ENABLED;
    
    return HAL_OK;
}

/**
 * @brief  设置PID参数
 * @param  pid: PID控制器指针
 * @param  params: PID参数指针
 * @retval HAL状态
 */
HAL_StatusTypeDef PID_SetParams(PID_Controller_t *pid, const PID_Params_t *params)
{
    if (pid == NULL || params == NULL) {
        return HAL_ERROR;
    }
    
    // 验证参数有效性
    if (!PID_ValidateParams(params)) {
        return HAL_ERROR;
    }
    
    // 复制参数
    memcpy(&pid->params, params, sizeof(PID_Params_t));
    
    return HAL_OK;
}

/**
 * @brief  设置默认PID参数
 * @param  pid: PID控制器指针
 * @retval HAL状态
 */
HAL_StatusTypeDef PID_SetDefaultParams(PID_Controller_t *pid)
{
    if (pid == NULL) {
        return HAL_ERROR;
    }
    
    pid->params.Kp = PID_DEFAULT_KP;
    pid->params.Ki = PID_DEFAULT_KI;
    pid->params.Kd = PID_DEFAULT_KD;
    pid->params.max_output = PID_DEFAULT_MAX_OUTPUT;
    pid->params.min_output = PID_DEFAULT_MIN_OUTPUT;
    pid->params.max_integral = PID_DEFAULT_MAX_INTEGRAL;
    pid->params.deadzone = PID_DEFAULT_DEADZONE;
    pid->params.integral_separation_threshold = PID_DEFAULT_INTEGRAL_SEP_THRESHOLD;
    pid->params.enable_integral_separation = 1;
    pid->params.enable_derivative_on_measurement = 0;
    
    return HAL_OK;
}

/**
 * @brief  重置PID控制器内部状态
 * @param  pid: PID控制器指针
 * @retval None
 */
void PID_Reset(PID_Controller_t *pid)
{
    if (pid == NULL) return;
    
    // 清零误差历史
    memset(pid->data.error, 0, sizeof(pid->data.error));
    
    // 重置内部状态
    pid->data.integral = 0.0f;
    pid->data.derivative = 0.0f;
    pid->data.last_output = 0.0f;
    pid->data.last_measurement = 0.0f;
    pid->data.last_time = HAL_GetTick();
    pid->data.update_count = 0;
    
    // 重置输入输出
    pid->setpoint = 0.0f;
    pid->feedback = 0.0f;
    pid->output = 0.0f;
    pid->dt = 0.0f;
}

/**
 * @brief  PID控制器更新 (手动指定时间间隔)
 * @param  pid: PID控制器指针
 * @param  setpoint: 目标值
 * @param  feedback: 反馈值
 * @param  dt: 时间间隔 (秒)
 * @retval 控制输出值
 */
float PID_Update(PID_Controller_t *pid, float setpoint, float feedback, float dt)
{
    if (pid == NULL || pid->state != PID_STATE_ENABLED) {
        return 0.0f;
    }
    
    // 检查时间间隔有效性
    if (dt <= 0.0f || dt > 1.0f) {
        return pid->output; // 返回上次输出
    }
    
    // 更新输入值
    pid->setpoint = setpoint;
    pid->feedback = feedback;
    pid->dt = dt;
    
    // 计算误差
    float error = setpoint - feedback;
    
    // 死区处理
    if (PID_ABS(error) < pid->params.deadzone) {
        error = 0.0f;
    }
    
    // 更新误差历史
    PID_UpdateErrorHistory(pid, error);
    
    // 根据PID类型计算输出
    float output = 0.0f;
    if (pid->type == PID_TYPE_POSITIONAL) {
        output = PID_CalculatePositional(pid);
    } else {
        output = PID_CalculateIncremental(pid);
    }
    
    // 输出限幅
    output = PID_CONSTRAIN(output, pid->params.min_output, pid->params.max_output);
    
    // 检查饱和状态
    if (output >= pid->params.max_output || output <= pid->params.min_output) {
        pid->state = PID_STATE_SATURATED;
    } else {
        pid->state = PID_STATE_ENABLED;
    }
    
    // 更新输出和状态
    pid->data.last_output = pid->output;
    pid->output = output;
    pid->data.last_measurement = feedback;
    pid->data.update_count++;
    
    return output;
}

/**
 * @brief  PID控制器更新 (自动计算时间间隔)
 * @param  pid: PID控制器指针
 * @param  setpoint: 目标值
 * @param  feedback: 反馈值
 * @retval 控制输出值
 */
float PID_UpdateWithTime(PID_Controller_t *pid, float setpoint, float feedback)
{
    if (pid == NULL) {
        return 0.0f;
    }
    
    // 计算时间间隔
    uint32_t current_time = HAL_GetTick();
    float dt = (current_time - pid->data.last_time) / 1000.0f; // 转换为秒
    pid->data.last_time = current_time;
    
    return PID_Update(pid, setpoint, feedback, dt);
}

/**
 * @brief  启用PID控制器
 * @param  pid: PID控制器指针
 * @retval None
 */
void PID_Enable(PID_Controller_t *pid)
{
    if (pid != NULL) {
        pid->state = PID_STATE_ENABLED;
    }
}

/**
 * @brief  禁用PID控制器
 * @param  pid: PID控制器指针
 * @retval None
 */
void PID_Disable(PID_Controller_t *pid)
{
    if (pid != NULL) {
        pid->state = PID_STATE_DISABLED;
        pid->output = 0.0f;
    }
}

/**
 * @brief  设置比例系数
 * @param  pid: PID控制器指针
 * @param  kp: 比例系数
 * @retval None
 */
void PID_SetKp(PID_Controller_t *pid, float kp)
{
    if (pid != NULL && kp >= 0.0f) {
        pid->params.Kp = kp;
    }
}

/**
 * @brief  设置积分系数
 * @param  pid: PID控制器指针
 * @param  ki: 积分系数
 * @retval None
 */
void PID_SetKi(PID_Controller_t *pid, float ki)
{
    if (pid != NULL && ki >= 0.0f) {
        pid->params.Ki = ki;
    }
}

/**
 * @brief  设置微分系数
 * @param  pid: PID控制器指针
 * @param  kd: 微分系数
 * @retval None
 */
void PID_SetKd(PID_Controller_t *pid, float kd)
{
    if (pid != NULL && kd >= 0.0f) {
        pid->params.Kd = kd;
    }
}

/**
 * @brief  设置输出限制
 * @param  pid: PID控制器指针
 * @param  min: 最小输出
 * @param  max: 最大输出
 * @retval None
 */
void PID_SetOutputLimits(PID_Controller_t *pid, float min, float max)
{
    if (pid != NULL && min < max) {
        pid->params.min_output = min;
        pid->params.max_output = max;
    }
}

/**
 * @brief  设置积分限幅
 * @param  pid: PID控制器指针
 * @param  max_integral: 积分限幅值
 * @retval None
 */
void PID_SetIntegralLimit(PID_Controller_t *pid, float max_integral)
{
    if (pid != NULL && max_integral > 0.0f) {
        pid->params.max_integral = max_integral;
    }
}

/**
 * @brief  设置死区
 * @param  pid: PID控制器指针
 * @param  deadzone: 死区范围
 * @retval None
 */
void PID_SetDeadzone(PID_Controller_t *pid, float deadzone)
{
    if (pid != NULL && deadzone >= 0.0f) {
        pid->params.deadzone = deadzone;
    }
}

/**
 * @brief  设置积分分离
 * @param  pid: PID控制器指针
 * @param  enable: 使能标志
 * @param  threshold: 分离阈值
 * @retval None
 */
void PID_SetIntegralSeparation(PID_Controller_t *pid, uint8_t enable, float threshold)
{
    if (pid != NULL) {
        pid->params.enable_integral_separation = enable;
        if (threshold > 0.0f) {
            pid->params.integral_separation_threshold = threshold;
        }
    }
}

/**
 * @brief  设置微分先行
 * @param  pid: PID控制器指针
 * @param  enable: 使能标志
 * @retval None
 */
void PID_SetDerivativeOnMeasurement(PID_Controller_t *pid, uint8_t enable)
{
    if (pid != NULL) {
        pid->params.enable_derivative_on_measurement = enable;
    }
}

/* Status Query Functions ----------------------------------------------------*/

/**
 * @brief  获取PID控制器状态
 * @param  pid: PID控制器指针
 * @retval PID状态
 */
PID_State_t PID_GetState(const PID_Controller_t *pid)
{
    if (pid == NULL) {
        return PID_STATE_ERROR;
    }
    return pid->state;
}

/**
 * @brief  获取当前误差
 * @param  pid: PID控制器指针
 * @retval 当前误差值
 */
float PID_GetError(const PID_Controller_t *pid)
{
    if (pid == NULL) {
        return 0.0f;
    }
    return pid->data.error[0];
}

/**
 * @brief  获取积分值
 * @param  pid: PID控制器指针
 * @retval 积分值
 */
float PID_GetIntegral(const PID_Controller_t *pid)
{
    if (pid == NULL) {
        return 0.0f;
    }
    return pid->data.integral;
}

/**
 * @brief  获取微分值
 * @param  pid: PID控制器指针
 * @retval 微分值
 */
float PID_GetDerivative(const PID_Controller_t *pid)
{
    if (pid == NULL) {
        return 0.0f;
    }
    return pid->data.derivative;
}

/**
 * @brief  获取输出值
 * @param  pid: PID控制器指针
 * @retval 输出值
 */
float PID_GetOutput(const PID_Controller_t *pid)
{
    if (pid == NULL) {
        return 0.0f;
    }
    return pid->output;
}

/**
 * @brief  获取更新计数
 * @param  pid: PID控制器指针
 * @retval 更新计数
 */
uint32_t PID_GetUpdateCount(const PID_Controller_t *pid)
{
    if (pid == NULL) {
        return 0;
    }
    return pid->data.update_count;
}

/* Utility Functions ---------------------------------------------------------*/

/**
 * @brief  打印PID状态信息
 * @param  pid: PID控制器指针
 * @retval None
 */
void PID_PrintStatus(const PID_Controller_t *pid)
{
    if (pid == NULL) {
        printf("PID: NULL pointer\r\n");
        return;
    }

    printf("=== PID Status ===\r\n");
    printf("Type: %s\r\n", (pid->type == PID_TYPE_POSITIONAL) ? "Positional" : "Incremental");
    printf("State: %d\r\n", pid->state);
    printf("Params: Kp=%.3f, Ki=%.3f, Kd=%.3f\r\n", pid->params.Kp, pid->params.Ki, pid->params.Kd);
    printf("Values: SP=%.2f, FB=%.2f, OUT=%.2f\r\n", pid->setpoint, pid->feedback, pid->output);
    printf("Error: %.2f, Integral: %.2f, Derivative: %.2f\r\n",
           pid->data.error[0], pid->data.integral, pid->data.derivative);
    printf("Updates: %u\r\n", (unsigned int)pid->data.update_count);
    printf("==================\r\n");
}

/**
 * @brief  PID自检测试
 * @param  pid: PID控制器指针
 * @retval HAL状态
 */
HAL_StatusTypeDef PID_SelfTest(PID_Controller_t *pid)
{
    if (pid == NULL) {
        return HAL_ERROR;
    }

    // 保存当前状态
    PID_Controller_t backup = *pid;

    // 测试基本功能
    PID_Reset(pid);

    // 测试参数设置
    PID_SetKp(pid, 1.0f);
    PID_SetKi(pid, 0.1f);
    PID_SetKd(pid, 0.01f);

    // 测试更新功能
    float output = PID_Update(pid, 10.0f, 0.0f, 0.01f);

    // 检查输出是否合理
    if (PID_ABS(output) < 0.001f || PID_ABS(output) > 1000.0f) {
        *pid = backup; // 恢复状态
        return HAL_ERROR;
    }

    // 恢复原始状态
    *pid = backup;

    return HAL_OK;
}

/* Private Functions ---------------------------------------------------------*/

/**
 * @brief  计算位置式PID输出
 * @param  pid: PID控制器指针
 * @retval PID输出值
 */
static float PID_CalculatePositional(PID_Controller_t *pid)
{
    float error = pid->data.error[0];

    // 比例项
    float proportional = pid->params.Kp * error;

    // 积分项 (带积分分离)
    float integral_term = 0.0f;
    if (pid->params.Ki > 0.0f) {
        // 积分分离判断
        if (!pid->params.enable_integral_separation ||
            PID_ABS(error) < pid->params.integral_separation_threshold) {
            pid->data.integral += error * pid->dt;
            // 积分限幅
            pid->data.integral = PID_CONSTRAIN(pid->data.integral,
                                             -pid->params.max_integral,
                                              pid->params.max_integral);
        }
        integral_term = pid->params.Ki * pid->data.integral;
    }

    // 微分项
    float derivative_term = 0.0f;
    if (pid->params.Kd > 0.0f && pid->dt > 0.0f) {
        if (pid->params.enable_derivative_on_measurement) {
            // 微分先行 (对测量值微分)
            pid->data.derivative = -(pid->feedback - pid->data.last_measurement) / pid->dt;
        } else {
            // 对误差微分
            pid->data.derivative = (error - pid->data.error[1]) / pid->dt;
        }
        derivative_term = pid->params.Kd * pid->data.derivative;
    }

    return proportional + integral_term + derivative_term;
}

/**
 * @brief  计算增量式PID输出
 * @param  pid: PID控制器指针
 * @retval PID增量输出值
 */
static float PID_CalculateIncremental(PID_Controller_t *pid)
{
    float error = pid->data.error[0];
    float error_1 = pid->data.error[1];
    float error_2 = pid->data.error[2];

    // 增量式PID公式: Δu = Kp*(e[k]-e[k-1]) + Ki*e[k] + Kd*(e[k]-2*e[k-1]+e[k-2])
    float delta_proportional = pid->params.Kp * (error - error_1);

    float delta_integral = 0.0f;
    if (pid->params.Ki > 0.0f) {
        // 积分分离判断
        if (!pid->params.enable_integral_separation ||
            PID_ABS(error) < pid->params.integral_separation_threshold) {
            delta_integral = pid->params.Ki * error * pid->dt;
        }
    }

    float delta_derivative = 0.0f;
    if (pid->params.Kd > 0.0f && pid->dt > 0.0f) {
        delta_derivative = pid->params.Kd * (error - 2*error_1 + error_2) / pid->dt;
    }

    float delta_output = delta_proportional + delta_integral + delta_derivative;

    // 增量式输出 = 上次输出 + 增量
    return pid->data.last_output + delta_output;
}

/**
 * @brief  更新误差历史
 * @param  pid: PID控制器指针
 * @param  error: 当前误差
 * @retval None
 */
static void PID_UpdateErrorHistory(PID_Controller_t *pid, float error)
{
    // 移位误差历史
    pid->data.error[2] = pid->data.error[1];
    pid->data.error[1] = pid->data.error[0];
    pid->data.error[0] = error;
}

/**
 * @brief  验证PID参数有效性
 * @param  params: PID参数指针
 * @retval 1: 有效, 0: 无效
 */
static uint8_t PID_ValidateParams(const PID_Params_t *params)
{
    if (params == NULL) return 0;

    // 检查参数范围
    if (params->Kp < 0.0f || params->Ki < 0.0f || params->Kd < 0.0f) return 0;
    if (params->max_output <= params->min_output) return 0;
    if (params->max_integral <= 0.0f) return 0;
    if (params->deadzone < 0.0f) return 0;

    return 1;
}

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
