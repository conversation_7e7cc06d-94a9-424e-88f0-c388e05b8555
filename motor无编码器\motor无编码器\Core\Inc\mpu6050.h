#ifndef __MPU6050_H
#define __MPU6050_H

#include "main.h"
#include "i2c.h"

// MPU6050设备地址 (7位地址左移1位)
#define MPU6050_ADDRESS     (0x68 << 1)

// MPU6050寄存器地址定义
#define MPU6050_WHO_AM_I        0x75  // 设备ID寄存器
#define MPU6050_PWR_MGMT_1      0x6B  // 电源管理1
#define MPU6050_PWR_MGMT_2      0x6C  // 电源管理2
#define MPU6050_SMPLRT_DIV      0x19  // 采样率分频器
#define MPU6050_CONFIG          0x1A  // 配置寄存器
#define MPU6050_GYRO_CONFIG     0x1B  // 陀螺仪配置
#define MPU6050_ACCEL_CONFIG    0x1C  // 加速度计配置
#define MPU6050_ACCEL_XOUT_H    0x3B  // 加速度X轴高字节
#define MPU6050_ACCEL_XOUT_L    0x3C  // 加速度X轴低字节
#define MPU6050_ACCEL_YOUT_H    0x3D  // 加速度Y轴高字节
#define MPU6050_ACCEL_YOUT_L    0x3E  // 加速度Y轴低字节
#define MPU6050_ACCEL_ZOUT_H    0x3F  // 加速度Z轴高字节
#define MPU6050_ACCEL_ZOUT_L    0x40  // 加速度Z轴低字节
#define MPU6050_TEMP_OUT_H      0x41  // 温度高字节
#define MPU6050_TEMP_OUT_L      0x42  // 温度低字节
#define MPU6050_GYRO_XOUT_H     0x43  // 陀螺仪X轴高字节
#define MPU6050_GYRO_XOUT_L     0x44  // 陀螺仪X轴低字节
#define MPU6050_GYRO_YOUT_H     0x45  // 陀螺仪Y轴高字节
#define MPU6050_GYRO_YOUT_L     0x46  // 陀螺仪Y轴低字节
#define MPU6050_GYRO_ZOUT_H     0x47  // 陀螺仪Z轴高字节
#define MPU6050_GYRO_ZOUT_L     0x48  // 陀螺仪Z轴低字节

// MPU6050数据结构
typedef struct {
    int16_t Accel_X;  // 加速度X轴原始数据
    int16_t Accel_Y;  // 加速度Y轴原始数据
    int16_t Accel_Z;  // 加速度Z轴原始数据
    int16_t Temp;     // 温度原始数据
    int16_t Gyro_X;   // 陀螺仪X轴原始数据
    int16_t Gyro_Y;   // 陀螺仪Y轴原始数据
    int16_t Gyro_Z;   // 陀螺仪Z轴原始数据
} MPU6050_Data;

// 函数声明
HAL_StatusTypeDef MPU6050_Init(void);
HAL_StatusTypeDef MPU6050_ReadData(MPU6050_Data *data);
HAL_StatusTypeDef MPU6050_WriteByte(uint8_t reg, uint8_t data);
HAL_StatusTypeDef MPU6050_ReadByte(uint8_t reg, uint8_t *data);
HAL_StatusTypeDef MPU6050_ReadBytes(uint8_t reg, uint8_t length, uint8_t *data);
uint8_t MPU6050_Test_Connection(void);

#endif /* __MPU6050_H */
