/**
 ******************************************************************************
 * @file    system_diagnostics.c
 * @brief   系统诊断工具实现文件
 * <AUTHOR> (工程师)
 * @version v1.0
 * @date    2025-01-15
 ******************************************************************************
 */

/* Includes ------------------------------------------------------------------*/
#include "system_diagnostics.h"
#include "i2c.h"
#include "tim.h"
#include "usart.h"
#include "gpio.h"
#include <string.h>

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/

/* Exported functions --------------------------------------------------------*/

/**
 * @brief  初始化系统诊断
 * @param  diag: 诊断结构体指针
 * @retval None
 */
void System_Diagnostics_Init(System_Diagnostics_t *diag)
{
    if (diag == NULL) return;
    
    memset(diag, 0, sizeof(System_Diagnostics_t));
    
    printf("\r\n=== System Diagnostics Tool ===\r\n");
    printf("Starting comprehensive system check...\r\n\r\n");
}

/**
 * @brief  运行所有诊断测试
 * @param  diag: 诊断结构体指针
 * @retval None
 */
void System_Diagnostics_RunAll(System_Diagnostics_t *diag)
{
    if (diag == NULL) return;
    
    printf("Running system diagnostics...\r\n\r\n");
    
    // 1. 时钟系统测试
    Diag_Result_t result = System_Diagnostics_TestClock();
    System_Diagnostics_AddResult(diag, "System Clock", result, 
        result == DIAG_RESULT_PASS ? "84MHz HSE PLL" : "Clock configuration error");
    
    // 2. GPIO测试
    result = System_Diagnostics_TestGPIO();
    System_Diagnostics_AddResult(diag, "GPIO Configuration", result,
        result == DIAG_RESULT_PASS ? "All pins configured" : "GPIO configuration error");
    
    // 3. UART测试
    result = System_Diagnostics_TestUART();
    System_Diagnostics_AddResult(diag, "UART Communication", result,
        result == DIAG_RESULT_PASS ? "115200 baud OK" : "UART communication error");
    
    // 4. I2C测试
    result = System_Diagnostics_TestI2C();
    System_Diagnostics_AddResult(diag, "I2C Bus", result,
        result == DIAG_RESULT_PASS ? "I2C1 400kHz OK" : "I2C bus error");
    
    // 5. MPU6050传感器测试
    result = System_Diagnostics_TestMPU6050();
    System_Diagnostics_AddResult(diag, "MPU6050 Sensor", result,
        result == DIAG_RESULT_PASS ? "Device ID 0x68 OK" : "Sensor connection failed");
    
    // 6. PWM测试
    result = System_Diagnostics_TestPWM();
    System_Diagnostics_AddResult(diag, "PWM Generation", result,
        result == DIAG_RESULT_PASS ? "TIM1 CH1/CH2 OK" : "PWM generation error");
    
    // 7. 电机控制测试
    result = System_Diagnostics_TestMotor();
    System_Diagnostics_AddResult(diag, "Motor Control", result,
        result == DIAG_RESULT_PASS ? "Direction pins OK" : "Motor control error");
    
    // 8. 内存测试
    result = System_Diagnostics_TestMemory();
    System_Diagnostics_AddResult(diag, "Memory System", result,
        result == DIAG_RESULT_PASS ? "RAM/Flash OK" : "Memory error");
    
    printf("\r\nDiagnostics completed!\r\n");
}

/**
 * @brief  打印诊断报告
 * @param  diag: 诊断结构体指针
 * @retval None
 */
void System_Diagnostics_PrintReport(const System_Diagnostics_t *diag)
{
    if (diag == NULL) return;
    
    printf("\r\n=== DIAGNOSTIC REPORT ===\r\n");
    printf("Total Tests: %d\r\n", diag->total_tests);
    printf("Passed: %d, Failed: %d, Warnings: %d\r\n", 
           diag->passed_tests, diag->failed_tests, diag->warning_tests);
    printf("Success Rate: %d%%\r\n", 
           diag->total_tests > 0 ? (diag->passed_tests * 100 / diag->total_tests) : 0);
    printf("\r\nDetailed Results:\r\n");
    
    for (uint8_t i = 0; i < diag->total_tests; i++) {
        printf("[%s] %-20s: %s\r\n", 
               System_Diagnostics_GetResultString(diag->items[i].result),
               diag->items[i].name,
               diag->items[i].message);
    }
    
    printf("\r\n");
    if (diag->failed_tests == 0) {
        printf("✅ ALL TESTS PASSED - System ready for operation!\r\n");
    } else {
        printf("❌ %d TESTS FAILED - Check hardware connections!\r\n", diag->failed_tests);
    }
    printf("========================\r\n\r\n");
}

/* Individual Test Functions -------------------------------------------------*/

/**
 * @brief  测试系统时钟
 * @retval 诊断结果
 */
Diag_Result_t System_Diagnostics_TestClock(void)
{
    printf("Testing system clock...\r\n");
    
    uint32_t sysclk = HAL_RCC_GetSysClockFreq();
    uint32_t hclk = HAL_RCC_GetHCLKFreq();
    uint32_t pclk1 = HAL_RCC_GetPCLK1Freq();
    uint32_t pclk2 = HAL_RCC_GetPCLK2Freq();
    
    printf("  SYSCLK: %lu Hz\r\n", sysclk);
    printf("  HCLK: %lu Hz\r\n", hclk);
    printf("  PCLK1: %lu Hz\r\n", pclk1);
    printf("  PCLK2: %lu Hz\r\n", pclk2);
    
    // 检查时钟频率是否合理 (期望84MHz)
    if (sysclk >= 80000000 && sysclk <= 90000000) {
        printf("  ✅ Clock frequencies OK\r\n");
        return DIAG_RESULT_PASS;
    } else {
        printf("  ❌ Clock frequency abnormal\r\n");
        return DIAG_RESULT_FAIL;
    }
}

/**
 * @brief  测试GPIO配置
 * @retval 诊断结果
 */
Diag_Result_t System_Diagnostics_TestGPIO(void)
{
    printf("Testing GPIO configuration...\r\n");
    
    // 测试电机方向控制引脚
    HAL_GPIO_WritePin(GPIOE, AIN1_Pin, GPIO_PIN_SET);
    HAL_Delay(1);
    if (HAL_GPIO_ReadPin(GPIOE, AIN1_Pin) == GPIO_PIN_SET) {
        HAL_GPIO_WritePin(GPIOE, AIN1_Pin, GPIO_PIN_RESET);
        printf("  ✅ Motor direction pins OK\r\n");
        return DIAG_RESULT_PASS;
    } else {
        printf("  ❌ Motor direction pins failed\r\n");
        return DIAG_RESULT_FAIL;
    }
}

/**
 * @brief  测试I2C总线
 * @retval 诊断结果
 */
Diag_Result_t System_Diagnostics_TestI2C(void)
{
    printf("Testing I2C bus...\r\n");
    
    // 扫描I2C总线上的设备
    uint8_t devices_found = 0;
    for (uint8_t addr = 0x08; addr < 0x78; addr++) {
        if (HAL_I2C_IsDeviceReady(&hi2c1, addr << 1, 1, 10) == HAL_OK) {
            printf("  Device found at address: 0x%02X\r\n", addr);
            devices_found++;
        }
    }
    
    if (devices_found > 0) {
        printf("  ✅ I2C bus OK, %d device(s) found\r\n", devices_found);
        return DIAG_RESULT_PASS;
    } else {
        printf("  ❌ No I2C devices found\r\n");
        printf("  Check: VCC, GND, SCL(PB6), SDA(PB7), pull-up resistors\r\n");
        return DIAG_RESULT_FAIL;
    }
}

/**
 * @brief  测试MPU6050传感器
 * @retval 诊断结果
 */
Diag_Result_t System_Diagnostics_TestMPU6050(void)
{
    printf("Testing MPU6050 sensor...\r\n");
    
    if (MPU6050_Test_Connection()) {
        printf("  ✅ MPU6050 connection OK\r\n");
        
        // 测试数据读取
        MPU6050_Data test_data;
        if (MPU6050_ReadData(&test_data) == HAL_OK) {
            printf("  ✅ MPU6050 data reading OK\r\n");
            printf("    Accel: X=%d, Y=%d, Z=%d\r\n", 
                   test_data.accel_x, test_data.accel_y, test_data.accel_z);
            printf("    Gyro: X=%d, Y=%d, Z=%d\r\n", 
                   test_data.gyro_x, test_data.gyro_y, test_data.gyro_z);
            return DIAG_RESULT_PASS;
        } else {
            printf("  ⚠️ MPU6050 data reading failed\r\n");
            return DIAG_RESULT_WARNING;
        }
    } else {
        printf("  ❌ MPU6050 connection failed\r\n");
        printf("  Check: I2C connection, device address (0x68)\r\n");
        return DIAG_RESULT_FAIL;
    }
}

/**
 * @brief  测试PWM生成
 * @retval 诊断结果
 */
Diag_Result_t System_Diagnostics_TestPWM(void)
{
    printf("Testing PWM generation...\r\n");
    
    // 启动PWM输出
    if (HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_1) == HAL_OK &&
        HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_2) == HAL_OK) {
        
        // 设置测试PWM值
        __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, 500);
        __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_2, 500);
        
        printf("  ✅ PWM generation OK\r\n");
        printf("    TIM1 CH1 (PE9): 50%% duty cycle\r\n");
        printf("    TIM1 CH2 (PE11): 50%% duty cycle\r\n");
        
        // 复位PWM值
        __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, 0);
        __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_2, 0);
        
        return DIAG_RESULT_PASS;
    } else {
        printf("  ❌ PWM generation failed\r\n");
        return DIAG_RESULT_FAIL;
    }
}

/**
 * @brief  测试电机控制
 * @retval 诊断结果
 */
Diag_Result_t System_Diagnostics_TestMotor(void)
{
    printf("Testing motor control...\r\n");
    
    // 测试方向控制引脚
    HAL_GPIO_WritePin(GPIOE, AIN1_Pin | AIN2_Pin | BIN1_Pin | BIN2_Pin, GPIO_PIN_RESET);
    HAL_Delay(10);
    
    // 测试前进
    HAL_GPIO_WritePin(GPIOE, AIN1_Pin | BIN1_Pin, GPIO_PIN_SET);
    HAL_Delay(10);
    HAL_GPIO_WritePin(GPIOE, AIN1_Pin | BIN1_Pin, GPIO_PIN_RESET);
    
    printf("  ✅ Motor direction control OK\r\n");
    return DIAG_RESULT_PASS;
}

/**
 * @brief  测试UART通信
 * @retval 诊断结果
 */
Diag_Result_t System_Diagnostics_TestUART(void)
{
    printf("Testing UART communication...\r\n");
    
    // UART测试通过printf输出本身就证明了UART工作正常
    printf("  ✅ UART communication OK\r\n");
    return DIAG_RESULT_PASS;
}

/**
 * @brief  测试内存系统
 * @retval 诊断结果
 */
Diag_Result_t System_Diagnostics_TestMemory(void)
{
    printf("Testing memory system...\r\n");
    
    // 简单的内存读写测试
    static uint32_t test_buffer[256];
    for (int i = 0; i < 256; i++) {
        test_buffer[i] = 0xAA55AA55 + i;
    }
    
    for (int i = 0; i < 256; i++) {
        if (test_buffer[i] != (0xAA55AA55 + i)) {
            printf("  ❌ Memory test failed\r\n");
            return DIAG_RESULT_FAIL;
        }
    }
    
    printf("  ✅ Memory system OK\r\n");
    return DIAG_RESULT_PASS;
}

/* Utility Functions ---------------------------------------------------------*/

/**
 * @brief  添加诊断结果
 * @param  diag: 诊断结构体指针
 * @param  name: 测试项目名称
 * @param  result: 测试结果
 * @param  message: 结果消息
 * @retval None
 */
void System_Diagnostics_AddResult(System_Diagnostics_t *diag, const char *name, 
                                  Diag_Result_t result, const char *message)
{
    if (diag == NULL || diag->total_tests >= 20) return;
    
    uint8_t index = diag->total_tests;
    diag->items[index].name = name;
    diag->items[index].result = result;
    diag->items[index].message = message;
    
    diag->total_tests++;
    
    switch (result) {
        case DIAG_RESULT_PASS:
            diag->passed_tests++;
            break;
        case DIAG_RESULT_FAIL:
            diag->failed_tests++;
            break;
        case DIAG_RESULT_WARNING:
            diag->warning_tests++;
            break;
    }
}

/**
 * @brief  获取结果字符串
 * @param  result: 诊断结果
 * @retval 结果字符串
 */
const char* System_Diagnostics_GetResultString(Diag_Result_t result)
{
    switch (result) {
        case DIAG_RESULT_PASS:    return "PASS";
        case DIAG_RESULT_FAIL:    return "FAIL";
        case DIAG_RESULT_WARNING: return "WARN";
        default:                  return "UNKN";
    }
}

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
