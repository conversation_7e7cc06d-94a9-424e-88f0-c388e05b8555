# PID平衡控制器系统集成图

## 1. 系统整体架构图

```mermaid
graph TB
    subgraph "应用层"
        A1[平衡控制器<br/>Balance Controller]
        A2[参数调节器<br/>Parameter Tuner]
        A3[监控调试器<br/>Debug Monitor]
    end
    
    subgraph "控制层"
        C1[PID控制器<br/>PID Controller]
        C2[电机控制器<br/>Motor Controller]
        C3[安全保护器<br/>Safety Guard]
    end
    
    subgraph "感知层"
        P1[姿态解算器<br/>Attitude Calculator]
        P2[数据滤波器<br/>Data Filter]
        P3[传感器接口<br/>Sensor Interface]
    end
    
    subgraph "硬件层"
        H1[MPU6050<br/>I2C1接口]
        H2[TB6612FNG<br/>PWM+GPIO]
        H3[STM32F407<br/>主控制器]
    end
    
    A1 --> C1
    A1 --> C2
    A1 --> C3
    A2 --> C1
    A3 --> A1
    
    C1 --> P1
    C2 --> H2
    C3 --> C2
    
    P1 --> P2
    P2 --> P3
    P3 --> H1
    
    H1 --> H3
    H2 --> H3
```

## 2. 数据流图

```mermaid
flowchart LR
    subgraph "传感器数据采集"
        S1[MPU6050传感器] --> S2[I2C数据读取]
        S2 --> S3[原始数据<br/>Accel + Gyro]
    end
    
    subgraph "姿态解算"
        S3 --> F1[数据滤波]
        F1 --> F2[互补滤波]
        F2 --> F3[姿态角度<br/>Pitch/Roll/Yaw]
    end
    
    subgraph "PID控制"
        F3 --> P1[角度误差计算]
        P1 --> P2[PID算法处理]
        P2 --> P3[控制量输出]
    end
    
    subgraph "电机控制"
        P3 --> M1[PWM信号生成]
        M1 --> M2[方向控制]
        M2 --> M3[电机驱动输出]
    end
    
    subgraph "物理反馈"
        M3 --> R1[车体运动]
        R1 --> R2[角度变化]
        R2 --> S1
    end
```

## 3. 控制时序图

```mermaid
sequenceDiagram
    participant Main as 主循环
    participant Sensor as 传感器模块
    participant Attitude as 姿态解算
    participant PID as PID控制器
    participant Motor as 电机控制
    
    loop 每5ms控制周期
        Main->>Sensor: 读取MPU6050数据
        Sensor-->>Main: 返回原始数据
        
        Main->>Attitude: 更新姿态解算
        Attitude-->>Main: 返回角度数据
        
        Main->>PID: 执行PID控制
        Note over PID: 计算角度误差<br/>执行PID算法
        PID-->>Main: 返回控制量
        
        Main->>Motor: 设置电机输出
        Note over Motor: PWM信号输出<br/>方向控制
        Motor-->>Main: 确认执行
    end
```

## 4. 模块接口关系图

```mermaid
classDiagram
    class PID_Controller {
        +PID_Type_t type
        +PID_Params_t params
        +float setpoint
        +float feedback
        +float output
        +PID_Update()
        +PID_Reset()
        +PID_SetParams()
    }
    
    class Balance_Controller {
        +PID_Controller_t angle_pid
        +Balance_Config_t config
        +Balance_Status_t status
        +Balance_Update()
        +Balance_Start()
        +Balance_Stop()
    }
    
    class Motor_Controller {
        +Motor_Config_t config
        +Motor_Status_t status
        +TIM_HandleTypeDef htim
        +Motor_SetSpeed()
        +Motor_SetDirection()
        +Motor_Stop()
    }
    
    class Attitude_Data {
        +float pitch
        +float roll
        +float yaw
        +float gyro_rate_x
        +float gyro_rate_y
        +float gyro_rate_z
    }
    
    Balance_Controller --> PID_Controller : 包含
    Balance_Controller --> Motor_Controller : 控制
    Balance_Controller --> Attitude_Data : 使用
    PID_Controller --> Attitude_Data : 读取反馈
```

## 5. 错误处理流程图

```mermaid
flowchart TD
    Start([系统启动]) --> Init[系统初始化]
    Init --> Check{初始化检查}
    Check -->|失败| Error1[初始化错误]
    Check -->|成功| Run[正常运行]
    
    Run --> Sensor[传感器读取]
    Sensor --> SensorCheck{传感器状态}
    SensorCheck -->|超时| Error2[传感器错误]
    SensorCheck -->|正常| Angle[角度计算]
    
    Angle --> AngleCheck{角度检查}
    AngleCheck -->|超限| Error3[角度超限错误]
    AngleCheck -->|正常| PID[PID控制]
    
    PID --> PIDCheck{PID状态}
    PIDCheck -->|饱和| Warning[PID饱和警告]
    PIDCheck -->|正常| Motor[电机控制]
    Warning --> Motor
    
    Motor --> MotorCheck{电机状态}
    MotorCheck -->|故障| Error4[电机故障]
    MotorCheck -->|正常| Run
    
    Error1 --> Stop[紧急停止]
    Error2 --> Retry[重试机制]
    Error3 --> Stop
    Error4 --> Stop
    
    Retry --> RetryCount{重试次数}
    RetryCount -->|<3次| Sensor
    RetryCount -->|≥3次| Stop
    
    Stop --> End([系统停止])
```

## 6. 内存布局图

```
STM32F407 内存布局 (相关部分)
┌─────────────────────────────────────┐ 0x20020000
│            SRAM (128KB)             │
├─────────────────────────────────────┤
│  系统栈区域 (16KB)                   │ 0x2001C000
├─────────────────────────────────────┤
│  HAL库数据区域 (8KB)                 │ 0x2001A000
├─────────────────────────────────────┤
│  PID控制器数据区域 (2KB)             │ 0x20019800
│  ├─ Balance_Controller_t (200B)     │
│  ├─ Motor_Controller_t (150B)       │
│  ├─ PID_Controller_t (100B)         │
│  └─ 其他控制数据 (1550B)             │
├─────────────────────────────────────┤
│  传感器数据缓冲区 (1KB)              │ 0x20019400
│  ├─ MPU6050_Data (14B)              │
│  ├─ Attitude_Data (32B)             │
│  └─ 历史数据缓冲 (978B)              │
├─────────────────────────────────────┤
│  应用程序数据区域 (剩余空间)          │ 0x20000000
└─────────────────────────────────────┘
```

## 7. 实时性分析图

```mermaid
gantt
    title PID控制系统实时性分析 (5ms控制周期)
    dateFormat X
    axisFormat %L
    
    section 传感器读取
    I2C数据读取    :0, 500
    数据解析      :500, 200
    
    section 姿态解算
    滤波计算      :700, 800
    角度计算      :1500, 500
    
    section PID控制
    误差计算      :2000, 100
    PID算法      :2100, 600
    输出限幅      :2700, 100
    
    section 电机控制
    PWM设置      :2800, 300
    GPIO控制     :3100, 200
    
    section 系统开销
    调试输出      :3300, 500
    系统管理      :3800, 200
    
    section 空闲时间
    空闲等待      :4000, 1000
```

---

**说明**: 
- 控制周期: 5ms (200Hz)
- 实际执行时间: ~4ms
- 系统余量: ~20%
- 关键路径: 传感器读取 → 姿态解算 → PID控制 → 电机输出
