# PID平衡控制器产品需求文档 (PRD)

## 1. 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-01-15
- **负责人**: Emma (产品经理)
- **项目名称**: STM32F4平衡车PID控制器
- **最后更新**: 2025-01-15

### 版本历史
| 版本 | 日期 | 修改内容 | 修改人 |
|------|------|----------|--------|
| v1.0 | 2025-01-15 | 初始版本创建 | Emma |

## 2. 背景与问题陈述

### 2.1 项目背景
当前STM32F4平衡车项目已具备以下基础功能：
- ✅ MPU6050姿态传感器数据采集
- ✅ 互补滤波和卡尔曼滤波姿态解算
- ✅ TB6612FNG双电机驱动控制
- ✅ 实时姿态角度计算（俯仰角、横滚角）

### 2.2 核心问题
**缺乏闭环控制系统**：现有系统只能读取姿态数据，无法根据倾斜角度自动调节电机输出，实现真正的平衡控制。

### 2.3 用户痛点
1. **无法自主平衡**：车体倾斜时无法自动纠正
2. **控制精度不足**：缺乏精确的角度控制算法
3. **响应速度慢**：没有快速响应机制
4. **参数调节困难**：缺乏系统化的参数优化方法

## 3. 目标与成功指标

### 3.1 项目目标 (Objectives)
- **O1**: 实现稳定的平衡控制，车体能在±5°范围内保持平衡
- **O2**: 提供完整的PID参数调节方案，支持自动和手动优化
- **O3**: 确保系统响应时间≤50ms，满足实时控制要求
- **O4**: 建立可扩展的控制架构，支持未来功能扩展

### 3.2 关键结果 (Key Results)
- **KR1**: 平衡精度：静态平衡误差≤±2°
- **KR2**: 响应速度：从倾斜到稳定≤1秒
- **KR3**: 稳定性：连续平衡时间≥30秒
- **KR4**: 参数收敛：自动调节在10次迭代内收敛

### 3.3 反向指标 (Counter Metrics)
- **CM1**: 电机过热：连续运行30分钟温升≤20°C
- **CM2**: 功耗控制：平均功耗≤5W
- **CM3**: 振荡抑制：稳态振荡幅度≤±1°

## 4. 用户画像与用户故事

### 4.1 目标用户
- **主要用户**: 电子竞赛参赛者、嵌入式开发工程师
- **次要用户**: 机器人爱好者、学生群体
- **技术水平**: 具备基础的嵌入式开发经验

### 4.2 用户故事
**作为一名电赛参赛者**，我希望：
- 快速实现平衡车的基本平衡功能
- 能够方便地调节PID参数以优化性能
- 获得稳定可靠的控制效果
- 有清晰的调试和优化指导

**作为一名嵌入式工程师**，我希望：
- 代码结构清晰，易于理解和修改
- 提供完整的技术文档和参数说明
- 支持不同的控制策略和算法选择
- 具备良好的可扩展性

## 5. 功能规格详述

### 5.1 核心功能模块

#### 5.1.1 PID控制器核心算法
**功能描述**: 实现标准PID控制算法，支持位置式和增量式两种模式

**输入参数**:
- 目标角度 (setpoint): 期望的平衡角度，通常为0°
- 当前角度 (feedback): MPU6050测量的实际俯仰角
- PID参数: Kp（比例系数）、Ki（积分系数）、Kd（微分系数）

**输出结果**:
- 控制量 (output): 电机PWM占空比调节值
- 调试信息: 各项误差值、控制量分量

**算法公式**:
```
位置式PID: output = Kp*error + Ki*∫error*dt + Kd*d(error)/dt
增量式PID: Δoutput = Kp*(error[k]-error[k-1]) + Ki*error[k] + Kd*(error[k]-2*error[k-1]+error[k-2])
```

#### 5.1.2 参数自适应调节
**功能描述**: 根据系统响应特性自动调节PID参数

**调节策略**:
- **Ziegler-Nichols方法**: 基于临界振荡特性
- **遗传算法优化**: 多参数全局寻优
- **模糊自适应**: 根据误差大小动态调节

**调节流程**:
1. 系统辨识：测量系统响应特性
2. 参数计算：根据响应特性计算初始PID参数
3. 在线优化：运行过程中微调参数
4. 性能评估：评估控制效果并记录最优参数

#### 5.1.3 电机控制集成
**功能描述**: 将PID输出转换为电机控制信号

**控制逻辑**:
```c
if (pid_output > 0) {
    // 前倾，需要前进
    left_motor_pwm = base_speed + pid_output;
    right_motor_pwm = base_speed + pid_output;
    motor_direction = FORWARD;
} else {
    // 后倾，需要后退
    left_motor_pwm = base_speed - pid_output;
    right_motor_pwm = base_speed - pid_output;
    motor_direction = BACKWARD;
}
```

**安全保护**:
- PWM限幅：防止电机过载
- 角度保护：倾斜角度过大时停止控制
- 超时保护：长时间无法平衡时自动停止

### 5.2 高级功能

#### 5.2.1 多级控制策略
- **粗调阶段**: 大角度偏差时使用大增益快速纠正
- **精调阶段**: 小角度偏差时使用小增益精确控制
- **稳态保持**: 接近平衡时使用最小控制量维持状态

#### 5.2.2 干扰抑制
- **低通滤波**: 滤除高频噪声
- **死区控制**: 小误差时不输出控制量
- **积分分离**: 大误差时暂停积分作用

#### 5.2.3 实时监控与调试
- **串口输出**: 实时显示PID参数和控制状态
- **参数在线调节**: 通过串口命令动态修改参数
- **性能指标统计**: 记录控制精度、响应时间等指标

## 6. 范围定义

### 6.1 包含功能 (In Scope)
✅ **核心PID算法实现**
- 位置式PID控制器
- 增量式PID控制器
- 参数限幅和保护机制

✅ **参数调节功能**
- 手动参数设置接口
- 自动参数优化算法
- 参数存储和恢复

✅ **电机控制集成**
- PID输出到PWM转换
- 双电机同步控制
- 方向控制逻辑

✅ **安全保护机制**
- 角度限制保护
- 电机过载保护
- 系统故障检测

✅ **调试和监控**
- 实时数据输出
- 参数在线调节
- 性能指标显示

### 6.2 排除功能 (Out of Scope)
❌ **高级运动控制**
- 路径规划和导航
- 遥控器控制接口
- 自动避障功能

❌ **复杂传感器融合**
- 视觉传感器集成
- 超声波测距
- GPS定位功能

❌ **无线通信功能**
- WiFi/蓝牙通信
- 远程监控界面
- 数据云端存储

## 7. 依赖与风险

### 7.1 内部依赖
- **硬件依赖**: STM32F407、MPU6050、TB6612FNG正常工作
- **软件依赖**: HAL库、姿态解算模块、定时器配置
- **数据依赖**: 准确的姿态角度数据、稳定的传感器读取

### 7.2 外部依赖
- **开发环境**: Keil MDK-ARM 5.x以上版本
- **调试工具**: ST-Link调试器、串口调试助手
- **测试环境**: 平整的测试场地、稳定的电源供应

### 7.3 潜在风险

#### 7.3.1 技术风险
| 风险 | 概率 | 影响 | 缓解措施 |
|------|------|------|----------|
| PID参数难以调节 | 中 | 高 | 提供多种自动调节算法 |
| 系统振荡不稳定 | 中 | 高 | 增加滤波和限幅机制 |
| 响应速度不足 | 低 | 中 | 优化算法和提高采样频率 |
| 电机控制精度低 | 低 | 中 | 增加PWM分辨率和校准 |

#### 7.3.2 项目风险
| 风险 | 概率 | 影响 | 缓解措施 |
|------|------|------|----------|
| 开发时间不足 | 中 | 中 | 采用模块化开发，优先核心功能 |
| 硬件故障 | 低 | 高 | 准备备用硬件和测试方案 |
| 需求变更 | 低 | 中 | 保持架构灵活性，支持快速修改 |

## 8. 发布初步计划

### 8.1 开发阶段
**阶段1: 核心算法开发** (预计2小时)
- PID控制器基础实现
- 基本的参数设置接口
- 简单的电机控制集成

**阶段2: 功能完善** (预计1.5小时)
- 参数自动调节算法
- 安全保护机制
- 调试监控功能

**阶段3: 测试优化** (预计1小时)
- 全面功能测试
- 性能优化调整
- 文档完善

### 8.2 测试计划
**单元测试**:
- PID算法正确性验证
- 参数调节功能测试
- 电机控制响应测试

**集成测试**:
- 整体平衡控制测试
- 长时间稳定性测试
- 极限条件测试

**性能测试**:
- 响应时间测量
- 控制精度评估
- 功耗和温升测试

### 8.3 交付物清单
1. **代码文件**
   - `pid_controller.h/c` - PID控制器核心实现
   - `balance_control.h/c` - 平衡控制主逻辑
   - `motor_control.h/c` - 电机控制接口
   - 更新的 `main.c` - 集成所有功能

2. **文档文件**
   - PID参数调节指南
   - 系统架构设计文档
   - 用户使用手册
   - 测试报告

3. **配置文件**
   - 默认PID参数配置
   - 系统校准参数
   - 调试输出配置

---

**文档状态**: ✅ 已完成  
**下一步**: 进入架构设计阶段  
**负责人**: Emma → Bob
