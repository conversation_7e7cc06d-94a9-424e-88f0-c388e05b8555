/**
 ******************************************************************************
 * @file    balance_system.c
 * @brief   平衡车系统集成实现文件 - 整合所有控制模块
 * <AUTHOR> (工程师)
 * @version v1.0
 * @date    2025-01-15
 ******************************************************************************
 */

/* Includes ------------------------------------------------------------------*/
#include "balance_system.h"
#include "tim.h"
#include <string.h>
#include <stdio.h>
#include <math.h>

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

// 全局系统实例
Balance_System_t g_balance_system;

/* Private function prototypes -----------------------------------------------*/
static void Balance_System_InitDWT(void);
static HAL_StatusTypeDef Balance_System_ReadSensors(Balance_System_t *system);
static void Balance_System_UpdateSystemStatus(Balance_System_t *system);
static void Balance_System_HandleError(Balance_System_t *system, uint8_t error_code);
static uint32_t Balance_System_GetMicroseconds(void);

/* Exported functions --------------------------------------------------------*/

/**
 * @brief  初始化平衡车系统
 * @param  system: 系统指针
 * @retval HAL状态
 */
HAL_StatusTypeDef Balance_System_Init(Balance_System_t *system)
{
    if (system == NULL) {
        return HAL_ERROR;
    }
    
    printf("=== Balance System Initialization ===\r\n");
    
    // 清零系统结构体
    memset(system, 0, sizeof(Balance_System_t));
    
    // 设置初始状态
    system->state = SYSTEM_STATE_INIT;
    system->mode = CONTROL_MODE_AUTO;
    
    // 初始化DWT计时器
    Balance_System_InitDWT();
    
    // 设置默认配置
    if (Balance_System_SetDefaultConfig(system) != HAL_OK) {
        system->state = SYSTEM_STATE_ERROR;
        return HAL_ERROR;
    }
    
    // 初始化MPU6050传感器
    printf("Initializing MPU6050 sensor...\r\n");
    if (MPU6050_Init() != HAL_OK) {
        printf("ERROR: MPU6050 initialization failed!\r\n");
        system->state = SYSTEM_STATE_ERROR;
        return HAL_ERROR;
    }
    
    // 初始化姿态解算模块
    printf("Initializing attitude calculation...\r\n");
    Attitude_Init();
    
    // 初始化平衡控制器
    printf("Initializing balance controller...\r\n");
    if (Balance_Init(&system->balance_controller) != HAL_OK) {
        printf("ERROR: Balance controller initialization failed!\r\n");
        system->state = SYSTEM_STATE_ERROR;
        return HAL_ERROR;
    }
    
    // 关联传感器数据
    Balance_AttachSensorData(&system->balance_controller, &system->sensor_data, &system->attitude_data);
    
    // 初始化电机控制器
    printf("Initializing motor controller...\r\n");
    if (Motor_Init(&system->motor_controller, &htim1) != HAL_OK) {
        printf("ERROR: Motor controller initialization failed!\r\n");
        system->state = SYSTEM_STATE_ERROR;
        return HAL_ERROR;
    }
    
    // 设置时间戳
    system->start_time = HAL_GetTick();
    system->last_control_time = system->start_time;
    system->last_debug_time = system->start_time;
    system->last_performance_time = system->start_time;
    
    // 启用调试输出
    system->debug_enabled = 1;
    system->performance_monitor_enabled = 1;
    
    // 设置为就绪状态
    system->state = SYSTEM_STATE_READY;
    
    printf("SUCCESS: Balance system initialized!\r\n");
    printf("======================================\r\n");
    
    return HAL_OK;
}

/**
 * @brief  设置系统配置
 * @param  system: 系统指针
 * @param  config: 配置参数指针
 * @retval HAL状态
 */
HAL_StatusTypeDef Balance_System_SetConfig(Balance_System_t *system, const System_Config_t *config)
{
    if (system == NULL || config == NULL) {
        return HAL_ERROR;
    }
    
    // 验证配置参数
    if (config->control_frequency <= 0.0f || config->control_frequency > 1000.0f) {
        return HAL_ERROR;
    }
    
    // 复制配置
    memcpy(&system->config, config, sizeof(System_Config_t));
    
    return HAL_OK;
}

/**
 * @brief  设置默认配置
 * @param  system: 系统指针
 * @retval HAL状态
 */
HAL_StatusTypeDef Balance_System_SetDefaultConfig(Balance_System_t *system)
{
    if (system == NULL) {
        return HAL_ERROR;
    }
    
    system->config.control_frequency = SYSTEM_DEFAULT_CONTROL_FREQ;
    system->config.debug_interval = SYSTEM_DEFAULT_DEBUG_INTERVAL;
    system->config.enable_auto_start = 0;
    system->config.enable_safety_check = 1;
    system->config.enable_data_logging = 0;
    system->config.startup_delay = SYSTEM_DEFAULT_STARTUP_DELAY;
    
    return HAL_OK;
}

/**
 * @brief  校准系统
 * @param  system: 系统指针
 * @retval HAL状态
 */
HAL_StatusTypeDef Balance_System_Calibrate(Balance_System_t *system)
{
    if (system == NULL || system->state < SYSTEM_STATE_READY) {
        return HAL_ERROR;
    }
    
    system->state = SYSTEM_STATE_CALIBRATING;
    
    printf("=== System Calibration ===\r\n");
    printf("Please keep the balance car stationary...\r\n");
    
    // 校准陀螺仪零点
    printf("Calibrating gyroscope...\r\n");
    Attitude_Calibrate();
    
    // 校准平衡角度
    printf("Calibrating balance angle...\r\n");
    if (Balance_Calibrate(&system->balance_controller) != HAL_OK) {
        system->state = SYSTEM_STATE_ERROR;
        return HAL_ERROR;
    }
    
    system->state = SYSTEM_STATE_READY;
    
    printf("SUCCESS: System calibration completed!\r\n");
    printf("===========================\r\n");
    
    return HAL_OK;
}

/**
 * @brief  启动系统
 * @param  system: 系统指针
 * @retval HAL状态
 */
HAL_StatusTypeDef Balance_System_Start(Balance_System_t *system)
{
    if (system == NULL || system->state != SYSTEM_STATE_READY) {
        return HAL_ERROR;
    }
    
    printf("=== Starting Balance System ===\r\n");
    
    // 启动延迟
    if (system->config.startup_delay > 0.0f) {
        printf("Startup delay: %.1f seconds...\r\n", system->config.startup_delay);
        HAL_Delay((uint32_t)(system->config.startup_delay * 1000));
    }
    
    // 启动平衡控制器
    Balance_Start(&system->balance_controller);
    
    // 启用电机控制器
    Motor_Enable(&system->motor_controller);
    
    // 重置性能统计
    memset(&system->performance, 0, sizeof(Performance_Stats_t));
    system->performance.loop_time_min = UINT32_MAX;
    
    // 设置运行状态
    system->state = SYSTEM_STATE_RUNNING;
    system->status.state = SYSTEM_STATE_RUNNING;
    
    printf("SUCCESS: Balance system started!\r\n");
    printf("Control frequency: %.1f Hz\r\n", system->config.control_frequency);
    printf("================================\r\n");
    
    return HAL_OK;
}

/**
 * @brief  停止系统
 * @param  system: 系统指针
 * @retval None
 */
void Balance_System_Stop(Balance_System_t *system)
{
    if (system == NULL) return;
    
    printf("=== Stopping Balance System ===\r\n");
    
    // 停止平衡控制器
    Balance_Stop(&system->balance_controller);
    
    // 停止电机
    Motor_Stop(&system->motor_controller);
    
    // 设置状态
    system->state = SYSTEM_STATE_READY;
    system->status.state = SYSTEM_STATE_READY;
    
    printf("Balance system stopped.\r\n");
    printf("================================\r\n");
}

/**
 * @brief  暂停系统
 * @param  system: 系统指针
 * @retval None
 */
void Balance_System_Pause(Balance_System_t *system)
{
    if (system == NULL || system->state != SYSTEM_STATE_RUNNING) return;
    
    // 停止电机但保持控制器运行
    Motor_Stop(&system->motor_controller);
    
    system->state = SYSTEM_STATE_PAUSED;
    system->status.state = SYSTEM_STATE_PAUSED;
    
    printf("Balance system paused.\r\n");
}

/**
 * @brief  恢复系统
 * @param  system: 系统指针
 * @retval None
 */
void Balance_System_Resume(Balance_System_t *system)
{
    if (system == NULL || system->state != SYSTEM_STATE_PAUSED) return;
    
    // 重新启用电机
    Motor_Enable(&system->motor_controller);
    
    system->state = SYSTEM_STATE_RUNNING;
    system->status.state = SYSTEM_STATE_RUNNING;
    
    printf("Balance system resumed.\r\n");
}

/**
 * @brief  紧急停止系统
 * @param  system: 系统指针
 * @retval None
 */
void Balance_System_EmergencyStop(Balance_System_t *system)
{
    if (system == NULL) return;
    
    printf("=== EMERGENCY STOP ===\r\n");
    
    // 紧急停止所有控制器
    Balance_EmergencyStop(&system->balance_controller);
    Motor_EmergencyStop(&system->motor_controller);
    
    // 设置紧急状态
    system->state = SYSTEM_STATE_EMERGENCY;
    system->status.state = SYSTEM_STATE_EMERGENCY;
    system->status.error_count++;
    system->status.last_error_time = HAL_GetTick();
    
    printf("System in emergency state!\r\n");
    printf("======================\r\n");
}

/**
 * @brief  重置系统
 * @param  system: 系统指针
 * @retval None
 */
void Balance_System_Reset(Balance_System_t *system)
{
    if (system == NULL) return;
    
    printf("=== Resetting Balance System ===\r\n");
    
    // 停止所有控制器
    Balance_Stop(&system->balance_controller);
    Motor_Stop(&system->motor_controller);
    
    // 重置控制器
    Balance_Reset(&system->balance_controller);
    Motor_Reset(&system->motor_controller);
    
    // 重置系统状态
    memset(&system->status, 0, sizeof(System_Status_t));
    memset(&system->performance, 0, sizeof(Performance_Stats_t));
    
    // 重置时间戳
    system->start_time = HAL_GetTick();
    system->last_control_time = system->start_time;
    system->last_debug_time = system->start_time;
    
    // 设置就绪状态
    system->state = SYSTEM_STATE_READY;
    system->status.state = SYSTEM_STATE_READY;
    
    printf("Balance system reset completed.\r\n");
    printf("=================================\r\n");
}

/* Main Loop Functions -------------------------------------------------------*/

/**
 * @brief  系统主循环更新
 * @param  system: 系统指针
 * @retval HAL状态
 */
HAL_StatusTypeDef Balance_System_Update(Balance_System_t *system)
{
    if (system == NULL || system->state < SYSTEM_STATE_READY) {
        return HAL_ERROR;
    }

    uint32_t loop_start_time = Balance_System_GetMicroseconds();

    // 读取传感器数据
    if (Balance_System_ReadSensors(system) != HAL_OK) {
        Balance_System_HandleError(system, 1);
        return HAL_ERROR;
    }

    // 执行控制任务
    if (system->state == SYSTEM_STATE_RUNNING) {
        if (Balance_System_ControlTask(system) != HAL_OK) {
            Balance_System_HandleError(system, 2);
            return HAL_ERROR;
        }
    }

    // 更新系统状态
    Balance_System_UpdateSystemStatus(system);

    // 调试输出
    if (system->debug_enabled) {
        uint32_t current_time = HAL_GetTick();
        if (current_time - system->last_debug_time >= system->config.debug_interval) {
            Balance_System_LogData(system);
            system->last_debug_time = current_time;
        }
    }

    // 性能统计
    if (system->performance_monitor_enabled) {
        uint32_t loop_time = Balance_System_GetMicroseconds() - loop_start_time;
        Balance_System_UpdatePerformanceStats(system, loop_time);
    }

    return HAL_OK;
}

/**
 * @brief  系统主循环 (阻塞式)
 * @param  system: 系统指针
 * @retval None
 */
void Balance_System_MainLoop(Balance_System_t *system)
{
    if (system == NULL) return;

    printf("=== Entering Main Loop ===\r\n");

    while (1) {
        // 更新系统
        Balance_System_Update(system);

        // 控制周期延迟
        HAL_Delay(SYSTEM_CONTROL_PERIOD_MS);

        // 检查紧急停止条件
        if (system->state == SYSTEM_STATE_EMERGENCY) {
            printf("Emergency state detected, exiting main loop.\r\n");
            break;
        }
    }
}

/**
 * @brief  控制任务
 * @param  system: 系统指针
 * @retval HAL状态
 */
HAL_StatusTypeDef Balance_System_ControlTask(Balance_System_t *system)
{
    if (system == NULL) return HAL_ERROR;

    uint32_t control_start_time = Balance_System_GetMicroseconds();

    // 更新平衡控制器
    if (Balance_Update(&system->balance_controller) != HAL_OK) {
        return HAL_ERROR;
    }

    // 获取控制输出
    float control_output = Balance_GetControlOutput(&system->balance_controller);

    // 转换为电机速度 (归一化到 -1.0 ~ 1.0)
    float motor_speed = control_output / 1000.0f; // 假设PID输出范围为 ±1000

    // 设置电机速度
    if (Motor_SetSpeed(&system->motor_controller, motor_speed) != HAL_OK) {
        return HAL_ERROR;
    }

    // 记录控制计算时间
    system->performance.control_calc_time = Balance_System_GetMicroseconds() - control_start_time;

    return HAL_OK;
}

/* Parameter Functions -------------------------------------------------------*/

/**
 * @brief  设置控制模式
 * @param  system: 系统指针
 * @param  mode: 控制模式
 * @retval None
 */
void Balance_System_SetControlMode(Balance_System_t *system, Control_Mode_t mode)
{
    if (system != NULL) {
        system->mode = mode;
        system->status.mode = mode;
        printf("Control mode set to: %s\r\n", Balance_System_GetModeString(mode));
    }
}

/**
 * @brief  设置PID参数
 * @param  system: 系统指针
 * @param  kp: 比例系数
 * @param  ki: 积分系数
 * @param  kd: 微分系数
 * @retval None
 */
void Balance_System_SetPIDParams(Balance_System_t *system, float kp, float ki, float kd)
{
    if (system != NULL) {
        Balance_SetPIDParams(&system->balance_controller, kp, ki, kd);
        printf("PID parameters updated: Kp=%.2f, Ki=%.2f, Kd=%.2f\r\n", kp, ki, kd);
    }
}

/**
 * @brief  设置目标角度
 * @param  system: 系统指针
 * @param  target_angle: 目标角度 (度)
 * @retval None
 */
void Balance_System_SetTargetAngle(Balance_System_t *system, float target_angle)
{
    if (system != NULL) {
        Balance_SetTargetAngle(&system->balance_controller, target_angle);
        printf("Target angle set to: %.2f°\r\n", target_angle);
    }
}

/**
 * @brief  PID参数自动调节
 * @param  system: 系统指针
 * @retval None
 */
void Balance_System_TunePIDParams(Balance_System_t *system)
{
    if (system == NULL) return;

    printf("=== PID Auto-Tuning ===\r\n");
    printf("Starting automatic PID parameter tuning...\r\n");

    // 简单的Ziegler-Nichols调节方法
    // 这里实现一个基础版本，实际应用中可以使用更复杂的算法

    float kp_test[] = {5.0f, 10.0f, 15.0f, 20.0f, 25.0f};
    float best_kp = 15.0f;
    float best_performance = 0.0f;

    for (int i = 0; i < 5; i++) {
        printf("Testing Kp = %.1f...\r\n", kp_test[i]);

        Balance_SetPIDParams(&system->balance_controller, kp_test[i], 0.5f, 0.8f);

        // 测试一段时间
        uint32_t test_start = HAL_GetTick();
        float total_error = 0.0f;
        int samples = 0;

        while (HAL_GetTick() - test_start < 2000) { // 测试2秒
            Balance_System_Update(system);

            float error = fabsf(Balance_GetCurrentAngle(&system->balance_controller));
            total_error += error;
            samples++;

            HAL_Delay(10);
        }

        float avg_error = total_error / samples;
        float performance = 1.0f / (1.0f + avg_error); // 误差越小性能越好

        printf("Average error: %.2f°, Performance: %.3f\r\n", avg_error, performance);

        if (performance > best_performance) {
            best_performance = performance;
            best_kp = kp_test[i];
        }
    }

    // 设置最佳参数
    Balance_SetPIDParams(&system->balance_controller, best_kp, 0.5f, 0.8f);

    printf("Best Kp found: %.1f (Performance: %.3f)\r\n", best_kp, best_performance);
    printf("PID auto-tuning completed.\r\n");
    printf("=======================\r\n");
}

/* Status Query Functions ----------------------------------------------------*/

/**
 * @brief  获取系统状态
 * @param  system: 系统指针
 * @retval 系统状态指针
 */
System_Status_t* Balance_System_GetStatus(Balance_System_t *system)
{
    if (system == NULL) {
        return NULL;
    }
    return &system->status;
}

/**
 * @brief  获取系统状态枚举
 * @param  system: 系统指针
 * @retval 系统状态
 */
System_State_t Balance_System_GetState(const Balance_System_t *system)
{
    if (system == NULL) {
        return SYSTEM_STATE_ERROR;
    }
    return system->state;
}

/**
 * @brief  获取性能统计
 * @param  system: 系统指针
 * @retval 性能统计指针
 */
Performance_Stats_t* Balance_System_GetPerformance(Balance_System_t *system)
{
    if (system == NULL) {
        return NULL;
    }
    return &system->performance;
}

/**
 * @brief  检查是否平衡
 * @param  system: 系统指针
 * @retval 1: 平衡, 0: 不平衡
 */
uint8_t Balance_System_IsBalanced(const Balance_System_t *system)
{
    if (system == NULL) {
        return 0;
    }
    return Balance_IsBalanced(&system->balance_controller);
}

/**
 * @brief  获取当前角度
 * @param  system: 系统指针
 * @retval 当前角度 (度)
 */
float Balance_System_GetCurrentAngle(const Balance_System_t *system)
{
    if (system == NULL) {
        return 0.0f;
    }
    return Balance_GetCurrentAngle(&system->balance_controller);
}

/**
 * @brief  获取控制输出
 * @param  system: 系统指针
 * @retval 控制输出值
 */
float Balance_System_GetControlOutput(const Balance_System_t *system)
{
    if (system == NULL) {
        return 0.0f;
    }
    return Balance_GetControlOutput(&system->balance_controller);
}

/* Debug and Monitoring Functions --------------------------------------------*/

/**
 * @brief  启用/禁用调试输出
 * @param  system: 系统指针
 * @param  enable: 启用标志
 * @retval None
 */
void Balance_System_EnableDebug(Balance_System_t *system, uint8_t enable)
{
    if (system != NULL) {
        system->debug_enabled = enable;
        printf("Debug output %s\r\n", enable ? "enabled" : "disabled");
    }
}

/**
 * @brief  打印系统状态
 * @param  system: 系统指针
 * @retval None
 */
void Balance_System_PrintStatus(const Balance_System_t *system)
{
    if (system == NULL) {
        printf("System: NULL pointer\r\n");
        return;
    }

    printf("=== Balance System Status ===\r\n");
    printf("State: %s | Mode: %s\r\n",
           Balance_System_GetStateString(system->state),
           Balance_System_GetModeString(system->mode));
    printf("Uptime: %lu ms | Cycles: %lu\r\n",
           system->status.uptime, system->status.control_cycles);
    printf("Balanced: %s | Balance Time: %lu ms\r\n",
           system->status.is_balanced ? "Yes" : "No", system->status.balance_time);
    printf("Current Angle: %.2f° | Control Output: %.1f\r\n",
           Balance_System_GetCurrentAngle(system), Balance_System_GetControlOutput(system));
    printf("Errors: %lu | Last Error: %lu ms ago\r\n",
           system->status.error_count,
           HAL_GetTick() - system->status.last_error_time);
    printf("==============================\r\n");
}

/**
 * @brief  打印性能统计
 * @param  system: 系统指针
 * @retval None
 */
void Balance_System_PrintPerformance(const Balance_System_t *system)
{
    if (system == NULL) {
        printf("System: NULL pointer\r\n");
        return;
    }

    printf("=== Performance Statistics ===\r\n");
    printf("Loop Time: Min=%lu us, Max=%lu us, Avg=%lu us\r\n",
           system->performance.loop_time_min,
           system->performance.loop_time_max,
           system->performance.loop_time_avg);
    printf("Sensor Read: %lu us | Control Calc: %lu us | Motor Update: %lu us\r\n",
           system->performance.sensor_read_time,
           system->performance.control_calc_time,
           system->performance.motor_update_time);
    printf("Total Samples: %lu | CPU Usage: %.1f%%\r\n",
           system->performance.total_samples, system->status.cpu_usage);
    printf("===============================\r\n");
}

/**
 * @brief  数据记录
 * @param  system: 系统指针
 * @retval None
 */
void Balance_System_LogData(Balance_System_t *system)
{
    if (system == NULL || !system->debug_enabled) return;

    // 格式化调试数据
    snprintf(system->debug_buffer, sizeof(system->debug_buffer),
             "T:%lu,S:%d,A:%.2f,E:%.2f,O:%.1f,B:%d,L:%d,R:%d",
             HAL_GetTick() - system->start_time,                    // 时间戳
             system->state,                                         // 系统状态
             Balance_System_GetCurrentAngle(system),                // 当前角度
             system->balance_controller.status.angle_error,        // 角度误差
             Balance_System_GetControlOutput(system),               // 控制输出
             Balance_System_IsBalanced(system),                     // 是否平衡
             Motor_GetPWMValue(&system->motor_controller, MOTOR_LEFT),  // 左电机PWM
             Motor_GetPWMValue(&system->motor_controller, MOTOR_RIGHT)  // 右电机PWM
    );

    printf("%s\r\n", system->debug_buffer);
}

/**
 * @brief  系统自检
 * @param  system: 系统指针
 * @retval HAL状态
 */
HAL_StatusTypeDef Balance_System_SelfTest(Balance_System_t *system)
{
    if (system == NULL) {
        return HAL_ERROR;
    }

    printf("=== System Self-Test ===\r\n");

    // 测试平衡控制器
    if (Balance_SelfTest(&system->balance_controller) != HAL_OK) {
        printf("Balance controller test failed!\r\n");
        return HAL_ERROR;
    }

    // 测试电机控制器
    if (Motor_SelfTest(&system->motor_controller) != HAL_OK) {
        printf("Motor controller test failed!\r\n");
        return HAL_ERROR;
    }

    printf("All tests passed!\r\n");
    printf("========================\r\n");

    return HAL_OK;
}

/* Utility Functions ---------------------------------------------------------*/

/**
 * @brief  获取状态字符串
 * @param  state: 状态枚举
 * @retval 状态字符串
 */
const char* Balance_System_GetStateString(System_State_t state)
{
    switch (state) {
        case SYSTEM_STATE_INIT:        return "INIT";
        case SYSTEM_STATE_CALIBRATING: return "CALIBRATING";
        case SYSTEM_STATE_READY:       return "READY";
        case SYSTEM_STATE_RUNNING:     return "RUNNING";
        case SYSTEM_STATE_PAUSED:      return "PAUSED";
        case SYSTEM_STATE_ERROR:       return "ERROR";
        case SYSTEM_STATE_EMERGENCY:   return "EMERGENCY";
        default:                       return "UNKNOWN";
    }
}

/**
 * @brief  获取模式字符串
 * @param  mode: 模式枚举
 * @retval 模式字符串
 */
const char* Balance_System_GetModeString(Control_Mode_t mode)
{
    switch (mode) {
        case CONTROL_MODE_MANUAL: return "MANUAL";
        case CONTROL_MODE_AUTO:   return "AUTO";
        case CONTROL_MODE_TUNING: return "TUNING";
        case CONTROL_MODE_TEST:   return "TEST";
        default:                  return "UNKNOWN";
    }
}

/**
 * @brief  更新性能统计
 * @param  system: 系统指针
 * @param  loop_time: 循环时间 (微秒)
 * @retval None
 */
void Balance_System_UpdatePerformanceStats(Balance_System_t *system, uint32_t loop_time)
{
    if (system == NULL) return;

    Performance_Stats_t *perf = &system->performance;

    // 更新循环时间统计
    if (loop_time < perf->loop_time_min) {
        perf->loop_time_min = loop_time;
    }
    if (loop_time > perf->loop_time_max) {
        perf->loop_time_max = loop_time;
    }

    // 计算平均值
    perf->total_samples++;
    perf->loop_time_avg = (perf->loop_time_avg * (perf->total_samples - 1) + loop_time) / perf->total_samples;

    // 计算CPU使用率 (基于循环时间)
    float expected_loop_time = 1000000.0f / system->config.control_frequency; // 期望循环时间 (微秒)
    system->status.cpu_usage = (loop_time / expected_loop_time) * 100.0f;

    // 限制CPU使用率显示范围
    if (system->status.cpu_usage > 100.0f) {
        system->status.cpu_usage = 100.0f;
    }
}

/* Private Functions ---------------------------------------------------------*/

/**
 * @brief  初始化DWT计时器 (用于微秒级计时)
 * @retval None
 */
static void Balance_System_InitDWT(void)
{
    // 启用DWT
    CoreDebug->DEMCR |= CoreDebug_DEMCR_TRCENA_Msk;

    // 重置计数器
    DWT->CYCCNT = 0;

    // 启用计数器
    DWT->CTRL |= DWT_CTRL_CYCCNTENA_Msk;
}

/**
 * @brief  读取传感器数据
 * @param  system: 系统指针
 * @retval HAL状态
 */
static HAL_StatusTypeDef Balance_System_ReadSensors(Balance_System_t *system)
{
    if (system == NULL) {
        return HAL_ERROR;
    }

    uint32_t sensor_start_time = Balance_System_GetMicroseconds();

    // 读取MPU6050数据
    if (MPU6050_ReadData(&system->sensor_data) != HAL_OK) {
        return HAL_ERROR;
    }

    // 更新姿态解算
    if (Attitude_Update(&system->sensor_data, &system->attitude_data) != HAL_OK) {
        return HAL_ERROR;
    }

    // 记录传感器读取时间
    system->performance.sensor_read_time = Balance_System_GetMicroseconds() - sensor_start_time;

    return HAL_OK;
}

/**
 * @brief  更新系统状态信息
 * @param  system: 系统指针
 * @retval None
 */
static void Balance_System_UpdateSystemStatus(Balance_System_t *system)
{
    if (system == NULL) return;

    uint32_t current_time = HAL_GetTick();

    // 更新运行时间
    system->status.uptime = current_time - system->start_time;

    // 更新控制周期计数
    system->status.control_cycles++;

    // 更新平衡状态
    system->status.is_balanced = Balance_IsBalanced(&system->balance_controller);
    if (system->status.is_balanced) {
        system->status.balance_time = Balance_GetBalanceTime(&system->balance_controller);
    } else {
        system->status.balance_time = 0;
    }

    // 更新系统状态
    system->status.state = system->state;
    system->status.mode = system->mode;
}

/**
 * @brief  处理系统错误
 * @param  system: 系统指针
 * @param  error_code: 错误代码
 * @retval None
 */
static void Balance_System_HandleError(Balance_System_t *system, uint8_t error_code)
{
    if (system == NULL) return;

    system->status.error_count++;
    system->status.last_error_time = HAL_GetTick();

    printf("System Error: Code %d, Count: %lu\r\n", error_code, system->status.error_count);

    // 根据错误严重程度决定处理方式
    if (system->status.error_count > 5) {
        printf("Too many errors, entering emergency stop!\r\n");
        Balance_System_EmergencyStop(system);
    }
}

/**
 * @brief  获取微秒时间戳
 * @retval 微秒时间戳
 */
static uint32_t Balance_System_GetMicroseconds(void)
{
    return DWT->CYCCNT / (SystemCoreClock / 1000000);
}

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
