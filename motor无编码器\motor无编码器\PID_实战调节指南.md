# 🎯 PID平衡控制器实战调节指南

## 🚀 系统已就绪！

恭喜！您的STM32F4平衡车已经集成了完整的PID控制系统，现在可以实现真正的平衡功能！

## 📋 快速检查清单

### ✅ 硬件连接确认
- [ ] MPU6050传感器已连接 (SCL-PB6, SDA-PB7)
- [ ] TB6612FNG电机驱动已连接 (PWM和方向控制引脚)
- [ ] 两个电机已连接到驱动器
- [ ] 串口调试线已连接 (TX-PA9, RX-PA10)
- [ ] 电源供电正常 (STM32: 5V, 电机: 6-12V)

### ✅ 软件准备确认
- [ ] 程序已编译无错误
- [ ] 程序已下载到STM32F407
- [ ] 串口调试助手已打开 (115200波特率)
- [ ] 平衡车机械结构稳固

## 🎮 实时PID参数调节

### 串口命令系统
您现在可以通过串口实时调节PID参数，无需重新编译程序！

#### 基本命令
```
kp 20.0          # 设置Kp为20.0
ki 0.8           # 设置Ki为0.8  
kd 1.2           # 设置Kd为1.2
pid 15.0 0.5 0.8 # 同时设置所有参数
get              # 查看当前参数
auto             # 启动自动调节
help             # 显示所有命令
```

#### 高级命令
```
save             # 保存当前参数
load             # 加载已保存参数
reset            # 恢复到备份参数
```

## 🔧 分步调节方法

### Step 1: 确定基础Kp值 (最重要！)

**目标**: 找到能让平衡车短暂保持平衡的最小Kp值

**操作步骤**:
1. 将平衡车放在平整地面上
2. 在串口中输入: `pid 10.0 0.0 0.0`
3. 观察平衡车反应:
   - **立即倾倒** → 增大Kp: `kp 15.0`
   - **剧烈振荡** → 减小Kp: `kp 8.0`
   - **能短暂平衡** → Kp值合适，进入下一步

**推荐测试序列**:
```
kp 5.0    # 测试是否太小
kp 10.0   # 测试基础值
kp 15.0   # 测试标准值
kp 20.0   # 测试是否过大
kp 25.0   # 测试上限
```

**成功标志**: 平衡车能保持直立3-5秒

### Step 2: 添加微分项Kd (稳定性)

**目标**: 减少振荡，提高稳定性

**操作步骤**:
1. 保持Step 1确定的Kp值
2. 在串口中输入: `kd 0.5`
3. 逐步增加Kd值:
   ```
   kd 0.5    # 轻微阻尼
   kd 0.8    # 标准阻尼
   kd 1.2    # 强阻尼
   kd 1.5    # 测试上限
   ```

**调节策略**:
- **仍有振荡** → 增大Kd: `kd 1.0`
- **响应变慢** → 减小Kd: `kd 0.6`
- **动作平滑** → Kd值合适

**成功标志**: 平衡车稳定直立，无明显振荡

### Step 3: 微调积分项Ki (消除偏差)

**目标**: 消除稳态角度偏差

**操作步骤**:
1. 保持前面确定的Kp和Kd值
2. 在串口中输入: `ki 0.3`
3. 逐步调节Ki值:
   ```
   ki 0.2    # 轻微积分
   ki 0.5    # 标准积分
   ki 0.8    # 强积分
   ki 1.0    # 测试上限
   ```

**调节策略**:
- **有角度偏差** → 增大Ki: `ki 0.6`
- **出现超调** → 减小Ki: `ki 0.3`
- **无偏差且稳定** → Ki值合适

**成功标志**: 平衡车长时间稳定，无角度偏差

## 🎯 不同场景的推荐参数

### 轻量级平衡车 (< 1kg)
```
pid 12.0 0.3 0.6
```

### 标准平衡车 (1-2kg)
```
pid 15.0 0.5 0.8
```

### 重型平衡车 (> 2kg)
```
pid 20.0 0.8 1.2
```

### 高精度控制 (竞赛级)
```
pid 18.0 0.6 1.0
```

## 🤖 自动调节功能

如果手动调节困难，可以使用自动调节功能：

### 启动自动调节
```
auto
```

系统会自动：
1. 测试多组Kp值 (5.0, 10.0, 15.0, 20.0, 25.0)
2. 评估每组参数的控制效果
3. 选择性能最佳的参数组合
4. 自动应用最优参数

**注意**: 自动调节需要约30秒，请确保平衡车处于安全环境

## 📊 实时监控数据

系统会持续输出实时数据：
```
T:12345,S:3,A:1.23,E:-0.45,O:123.4,B:1,L:456,R:456
```

**关键指标**:
- **A (角度)**: 应在±2°以内
- **E (误差)**: 应接近0
- **B (平衡)**: 应为1 (平衡状态)
- **L/R (PWM)**: 应有合理的输出值

## 🔍 常见问题解决

### 问题1: 平衡车立即倾倒
**现象**: 上电后立即倾倒，无法自立
**原因**: Kp值太小
**解决**: 
```
kp 25.0  # 大幅增加Kp值
```

### 问题2: 剧烈左右摆动
**现象**: 平衡车快速左右摆动
**原因**: Kp过大或Kd不足
**解决**:
```
kp 10.0  # 减小Kp值
kd 1.5   # 增大Kd值
```

### 问题3: 缓慢振荡
**现象**: 平衡车缓慢前后摆动
**原因**: Ki值过大
**解决**:
```
ki 0.2   # 减小Ki值
```

### 问题4: 有角度偏差
**现象**: 平衡但倾斜一定角度
**原因**: Ki不足或需要重新校准
**解决**:
```
ki 0.8   # 增大Ki值
# 或重启系统重新校准
```

### 问题5: 电机不转
**检查**:
1. 观察串口L/R数值是否变化
2. 检查电机供电 (6-12V)
3. 检查TB6612连接
4. 测试命令: `get` 查看参数是否正常

### 问题6: 传感器无数据
**检查**:
1. 观察串口是否有"ERROR: Sensor reading failed!"
2. 检查MPU6050供电 (3.3V)
3. 检查I2C连接 (SCL-PB6, SDA-PB7)

## 🏆 调节成功标准

当您看到以下现象时，说明PID调节成功：

✅ **自主直立**: 平衡车能够自主保持直立状态  
✅ **快速恢复**: 轻推后能在1秒内恢复平衡  
✅ **长时间稳定**: 能连续平衡30秒以上  
✅ **无振荡**: 动作平滑，无明显摆动  
✅ **角度精度**: 角度误差在±2°以内  
✅ **响应灵敏**: 对外界干扰反应迅速  

## 💡 高级调节技巧

### 1. 分阶段调节
```
# 阶段1: 粗调 (快速响应)
pid 20.0 0.2 1.2

# 阶段2: 精调 (平衡控制)  
pid 15.0 0.5 0.8

# 阶段3: 微调 (精确控制)
pid 12.0 0.8 0.5
```

### 2. 环境适应性调节
- **光滑地面**: 增大Kd值
- **粗糙地面**: 减小Kd值
- **重心偏高**: 增大Kp值
- **重心偏低**: 减小Kp值

### 3. 参数保存策略
```
# 找到好参数后立即保存
save

# 测试新参数前先备份
get    # 记录当前参数

# 测试失败后恢复
reset  # 恢复到备份参数
```

## 🎊 恭喜成功！

当您的平衡车能够稳定平衡时，您已经成功掌握了PID控制技术！

**您现在拥有**:
- ✅ 完整的PID平衡控制系统
- ✅ 实时参数调节能力
- ✅ 自动优化功能
- ✅ 专业级控制效果

**下一步可以**:
- 尝试不同的控制策略
- 添加遥控功能
- 实现路径跟踪
- 开发更高级的控制算法

---

**记住**: PID调节是一门艺术，需要耐心和实践。每个平衡车都有其独特的特性，找到最适合的参数组合是成功的关键！

**技术支持**: 如有问题，请查看项目文档或联系技术支持团队。
