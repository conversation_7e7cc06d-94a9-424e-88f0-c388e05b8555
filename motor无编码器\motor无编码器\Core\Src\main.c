/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "dma.h"
#include "i2c.h"
#include "tim.h"
#include "usart.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "mpu6050.h"
#include "attitude.h"
#include "balance_system.h"
#include "pid_tuner.h"
#include "system_diagnostics.h"
#include <stdio.h>
#include <string.h>
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */
uint8_t uart_rx_buffer;  // 串口接收缓冲区

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{

  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_DMA_Init();
  MX_USART1_UART_Init();
  MX_I2C1_Init();
  MX_TIM1_Init();
  MX_I2C3_Init();
  MX_TIM3_Init();
  MX_TIM4_Init();
  MX_TIM2_Init();
  MX_USART2_UART_Init();
  /* USER CODE BEGIN 2 */

  // 最简单的串口测试 - 不使用printf，直接使用HAL函数
  const char* test_msg1 = "UART Test: Hello World!\r\n";
  const char* test_msg2 = "STM32F407 Ready!\r\n";
  const char* test_msg3 = "Using USART1 PA9/PA10\r\n";

  HAL_UART_Transmit(&huart1, (uint8_t*)test_msg1, 25, 1000);
  HAL_Delay(500);
  HAL_UART_Transmit(&huart1, (uint8_t*)test_msg2, 18, 1000);
  HAL_Delay(500);
  HAL_UART_Transmit(&huart1, (uint8_t*)test_msg3, 23, 1000);
  HAL_Delay(500);

  // 测试printf是否工作
  HAL_UART_Transmit(&huart1, (uint8_t*)"Testing printf...\r\n", 19, 1000);
  printf("Printf test: If you see this, printf works!\r\n");
  HAL_UART_Transmit(&huart1, (uint8_t*)"Printf test completed\r\n", 23, 1000);

  // 逐步测试各个模块的初始化
  HAL_UART_Transmit(&huart1, (uint8_t*)"Step 1: Basic system test\r\n", 27, 1000);

  // 测试MPU6050初始化
  HAL_UART_Transmit(&huart1, (uint8_t*)"Step 2: Testing MPU6050...\r\n", 29, 1000);
  if(MPU6050_Init() == HAL_OK) {
    HAL_UART_Transmit(&huart1, (uint8_t*)"MPU6050 Init OK\r\n", 17, 1000);
  } else {
    HAL_UART_Transmit(&huart1, (uint8_t*)"MPU6050 Init FAILED\r\n", 21, 1000);
  }

  // 测试姿态解算初始化
  HAL_UART_Transmit(&huart1, (uint8_t*)"Step 3: Testing Attitude...\r\n", 30, 1000);
  Attitude_Init();
  HAL_UART_Transmit(&huart1, (uint8_t*)"Attitude Init OK\r\n", 18, 1000);

  HAL_UART_Transmit(&huart1, (uint8_t*)"All basic tests completed\r\n", 27, 1000);

  // 暂时跳过复杂的平衡系统初始化
  HAL_UART_Transmit(&huart1, (uint8_t*)"Step 4: Skipping Balance_System_Init for now\r\n", 47, 1000);

  /*
  // Initialize the complete balance system
  if(Balance_System_Init(&g_balance_system) != HAL_OK) {
    printf("ERROR: Balance system initialization failed! System stopped\r\n");
    Error_Handler();
  }
  */

  // 暂时跳过所有复杂的初始化
  HAL_UART_Transmit(&huart1, (uint8_t*)"Step 5: Skipping calibration and PID init\r\n", 44, 1000);

  /*
  // System calibration
  printf("\r\nStarting system calibration...\r\n");
  printf("Please keep the balance car upright and stationary!\r\n");
  HAL_Delay(3000);  // Give user time to position the car

  if(Balance_System_Calibrate(&g_balance_system) != HAL_OK) {
    printf("ERROR: System calibration failed!\r\n");
    Error_Handler();
  }

  // Initialize PID parameter tuner
  printf("Initializing PID parameter tuner...\r\n");
  if(PID_Tuner_Init(&g_pid_tuner, &g_balance_system) != HAL_OK) {
    printf("ERROR: PID tuner initialization failed!\r\n");
    Error_Handler();
  }

  // Set initial PID parameters (can be tuned later)
  printf("Setting initial PID parameters...\r\n");
  Balance_System_SetPIDParams(&g_balance_system, 15.0f, 0.5f, 0.8f);

  // Enable debug output
  Balance_System_EnableDebug(&g_balance_system, 1);
  */

  printf("\r\n=== System Ready ===\r\n");
  printf("Commands:\r\n");
  printf("- System will auto-start balance control in 2 seconds\r\n");
  printf("- Tilt the car gently to test balance response\r\n");
  printf("- Monitor serial output for real-time data\r\n");
  printf("- Use serial commands to tune PID parameters:\r\n");
  printf("  * kp <value>  - Set Kp parameter\r\n");
  printf("  * ki <value>  - Set Ki parameter\r\n");
  printf("  * kd <value>  - Set Kd parameter\r\n");
  printf("  * auto        - Start auto-tuning\r\n");
  printf("  * help        - Show all commands\r\n\r\n");

  // Start UART receive interrupt for PID tuning commands
  HAL_UART_Transmit(&huart1, (uint8_t*)"Step 6: Starting UART interrupt\r\n", 33, 1000);
  HAL_UART_Receive_IT(&huart1, &uart_rx_buffer, 1);  // 使用USART1

  // 暂时跳过平衡系统启动
  HAL_UART_Transmit(&huart1, (uint8_t*)"Step 7: Skipping Balance_System_Start\r\n", 39, 1000);

  /*
  // Auto-start delay
  HAL_Delay(2000);

  // Start the balance system
  if(Balance_System_Start(&g_balance_system) != HAL_OK) {
    printf("ERROR: Failed to start balance system!\r\n");
    Error_Handler();
  }
  */

  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */

  HAL_UART_Transmit(&huart1, (uint8_t*)"Step 8: Entering main loop\r\n", 29, 1000);
  printf("=== Main Loop Started ===\r\n");

  uint32_t counter = 0;
  uint32_t last_time = HAL_GetTick();

  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */

    uint32_t current_time = HAL_GetTick();

    // 每1秒输出一次测试信息
    if (current_time - last_time >= 1000) {
      counter++;

      // 方法1: 直接使用HAL_UART_Transmit
      char buffer[50];
      sprintf(buffer, "Test %u: Tick = %u\r\n", (unsigned int)counter, (unsigned int)current_time);
      HAL_UART_Transmit(&huart1, (uint8_t*)buffer, strlen(buffer), 1000);

      // 方法2: 尝试printf
      printf("Printf Test %u: System OK\r\n", (unsigned int)counter);

      last_time = current_time;

      // 闪烁LED指示程序运行
      HAL_GPIO_TogglePin(GPIOD, GPIO_PIN_12);
      HAL_GPIO_TogglePin(GPIOD, GPIO_PIN_13);
      HAL_GPIO_TogglePin(GPIOD, GPIO_PIN_14);
      HAL_GPIO_TogglePin(GPIOD, GPIO_PIN_15);
    }

    // Check system state
    System_State_t current_state = Balance_System_GetState(&g_balance_system);

    if(current_state == SYSTEM_STATE_EMERGENCY) {
      printf("System in emergency state, stopping main loop.\r\n");
      break;
    }

    // 5ms delay (200Hz control frequency)
    HAL_Delay(5);

  }

  // If we exit the main loop, ensure everything is stopped
  printf("Exiting main loop, stopping all systems...\r\n");
  Balance_System_EmergencyStop(&g_balance_system);

  /* USER CODE END 3 */
}

/* USER CODE BEGIN 4 */

/**
 * @brief  串口接收完成回调函数
 * @param  huart: 串口句柄
 * @retval None
 */
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
  if (huart->Instance == USART1) {  // 使用USART1
    // 处理接收到的字符
    PID_Tuner_ParseSerialInput(&g_pid_tuner, uart_rx_buffer);

    // 重新启动接收
    HAL_UART_Receive_IT(&huart1, &uart_rx_buffer, 1);  // 使用USART1
  }
}

/* USER CODE END 4 */


/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Configure the main internal regulator output voltage
  */
  __HAL_RCC_PWR_CLK_ENABLE();
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSE;
  RCC_OscInitStruct.HSEState = RCC_HSE_ON;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
  RCC_OscInitStruct.PLL.PLLM = 4;
  RCC_OscInitStruct.PLL.PLLN = 168;
  RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV2;
  RCC_OscInitStruct.PLL.PLLQ = 4;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV4;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV2;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_5) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */

// Serial port redirection - support printf output to UART
#ifdef __GNUC__
#define PUTCHAR_PROTOTYPE int __io_putchar(int ch)
#else
#define PUTCHAR_PROTOTYPE int fputc(int ch, FILE *f)
#endif

PUTCHAR_PROTOTYPE {
    HAL_UART_Transmit(&huart1, (uint8_t *)&ch, 1, 0xFFFF);  // 使用USART1
    return ch;
}

/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
