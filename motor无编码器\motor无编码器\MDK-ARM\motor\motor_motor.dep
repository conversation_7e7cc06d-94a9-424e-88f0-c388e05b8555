Dependencies for Project 'motor', Target 'motor': (DO NOT MODIFY !)
CompilerVersion: 6210000::V6.21::ARMCLANG
F (startup_stm32f407xx.s)(0x6875F216)(--target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -masm=auto  -Wa,armasm,--diag_suppress=A1950W -c

-gdwarf-4 -Wa,armasm,--pd,"__MICROLIB SETA 1"

-I./RTE/_motor

-ID:/blue/my_program/app_uv/ARM/Packs/ARM/CMSIS/5.9.0/CMSIS/Core/Include

-Wa,armasm,--pd,"__UVISION_VERSION SETA 539" -Wa,armasm,--pd,"_RTE_ SETA 1" -Wa,armasm,--pd,"STM32F407xx SETA 1" -Wa,armasm,--pd,"_RTE_ SETA 1"

-o motor/startup_stm32f407xx.o)
F (../Core/Src/main.c)(0x687721C9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_motor

-ID:/blue/my_program/app_uv/ARM/Packs/ARM/CMSIS/5.9.0/CMSIS/Core/Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F407xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F407xx

-o motor/main.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x684904BC)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6875F214)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x684904BC)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f407xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x684904BA)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_version.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_compiler.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_armclang.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\mpu_armv7.h)(0x00000000)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x684904BA)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684904BC)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x684904BC)
I (..\Core\Inc\dma.h4.\Core\Inc\i2c.hc.\Core\Inc\tim.h)(0x00000000)
I (..\Core\Inc\usart.h\.\Core\Inc\gpio.hC.\Core\Inc\mpu6050.h)(0x00000000)
I (..\Core\Inc\attitude.h)(0x68763978)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\math.h)(0x6569B012)
I (..\Core\Inc\balance_system.hM.\Core\Inc\pid_controller.h)(0x00000000)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\string.h)(0x6569B012)
I (..\Core\Inc\balance_control.h\.\Core\Inc\motor_control.h)(0x00000000)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdio.h)(0x6569B012)
I (..\Core\Inc\pid_tuner.h)(0x68766F8D)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdlib.h)(0x6569B012)
I (..\Core\Inc\system_diagnostics.h)(0x6877111B)
F (../Core/Src/gpio.c)(0x6875F212)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_motor

-ID:/blue/my_program/app_uv/ARM/Packs/ARM/CMSIS/5.9.0/CMSIS/Core/Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F407xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F407xx

-o motor/gpio.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x684904BC)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6875F214)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x684904BC)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f407xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x684904BA)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_version.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_compiler.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_armclang.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\mpu_armv7.h)(0x00000000)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x684904BA)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684904BC)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x684904BC)
F (../Core/Src/dma.c)(0x6875F212)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_motor

-ID:/blue/my_program/app_uv/ARM/Packs/ARM/CMSIS/5.9.0/CMSIS/Core/Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F407xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F407xx

-o motor/dma.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x684904BC)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6875F214)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x684904BC)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f407xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x684904BA)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_version.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_compiler.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_armclang.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\mpu_armv7.h)(0x00000000)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x684904BA)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684904BC)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x684904BC)
F (../Core/Src/i2c.c)(0x6875F212)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_motor

-ID:/blue/my_program/app_uv/ARM/Packs/ARM/CMSIS/5.9.0/CMSIS/Core/Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F407xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F407xx

-o motor/i2c.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x684904BC)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6875F214)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x684904BC)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f407xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x684904BA)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_version.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_compiler.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_armclang.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\mpu_armv7.h)(0x00000000)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x684904BA)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684904BC)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x684904BC)
F (../Core/Src/tim.c)(0x6875F212)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_motor

-ID:/blue/my_program/app_uv/ARM/Packs/ARM/CMSIS/5.9.0/CMSIS/Core/Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F407xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F407xx

-o motor/tim.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x684904BC)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6875F214)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x684904BC)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f407xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x684904BA)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_version.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_compiler.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_armclang.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\mpu_armv7.h)(0x00000000)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x684904BA)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684904BC)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x684904BC)
F (../Core/Src/usart.c)(0x6875F212)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_motor

-ID:/blue/my_program/app_uv/ARM/Packs/ARM/CMSIS/5.9.0/CMSIS/Core/Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F407xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F407xx

-o motor/usart.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x684904BC)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6875F214)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x684904BC)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f407xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x684904BA)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_version.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_compiler.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_armclang.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\mpu_armv7.h)(0x00000000)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x684904BA)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684904BC)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x684904BC)
F (../Core/Src/stm32f4xx_it.c)(0x6875F214)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_motor

-ID:/blue/my_program/app_uv/ARM/Packs/ARM/CMSIS/5.9.0/CMSIS/Core/Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F407xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F407xx

-o motor/stm32f4xx_it.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x684904BC)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6875F214)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x684904BC)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f407xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x684904BA)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_version.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_compiler.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_armclang.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\mpu_armv7.h)(0x00000000)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x684904BA)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684904BC)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x684904BC)
I (..\Core\Inc\stm32f4xx_it.h)(0x6875F214)
F (../Core/Src/stm32f4xx_hal_msp.c)(0x6875F214)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_motor

-ID:/blue/my_program/app_uv/ARM/Packs/ARM/CMSIS/5.9.0/CMSIS/Core/Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F407xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F407xx

-o motor/stm32f4xx_hal_msp.o -MD)
I (..\Core\Inc\main.h4.\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x00000000)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6875F214)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x684904BC)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f407xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x684904BA)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_version.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_compiler.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_armclang.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\mpu_armv7.h)(0x00000000)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x684904BA)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684904BC)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x684904BC)
F (../Core/Src/mpu6050.c)(0x68763E62)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_motor

-ID:/blue/my_program/app_uv/ARM/Packs/ARM/CMSIS/5.9.0/CMSIS/Core/Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F407xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F407xx

-o motor/mpu6050.o -MD)
I (..\Core\Inc\main.h0.\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x00000000)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6875F214)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x684904BC)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f407xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x684904BA)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_version.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_compiler.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_armclang.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\mpu_armv7.h)(0x00000000)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x684904BA)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684904BC)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x684904BC)
I (..\Core\Inc\i2c.h)(0x6875F212)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdio.h)(0x6569B012)
F (../Core/Src/attitude.c)(0x68763EFA)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_motor

-ID:/blue/my_program/app_uv/ARM/Packs/ARM/CMSIS/5.9.0/CMSIS/Core/Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F407xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F407xx

-o motor/attitude.o -MD)
I (..\Core\Inc\main.hd.\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x00000000)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6875F214)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x684904BC)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f407xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x684904BA)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_version.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_compiler.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_armclang.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\mpu_armv7.h)(0x00000000)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x684904BA)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684904BC)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x684904BC)
I (..\Core\Inc\mpu6050.hH.\Core\Inc\i2c.h)(0x00000000)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\math.h)(0x6569B012)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdio.h)(0x6569B012)
F (../Core/Src/pid_controller.c)(0x6877128A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_motor

-ID:/blue/my_program/app_uv/ARM/Packs/ARM/CMSIS/5.9.0/CMSIS/Core/Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F407xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F407xx

-o motor/pid_controller.o -MD)
I (..\Core\Inc\pid_controller.h)(0x68766889)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x684904BC)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6875F214)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x684904BC)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f407xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x684904BA)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_version.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_compiler.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_armclang.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\mpu_armv7.h)(0x00000000)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x684904BA)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684904BC)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x684904BC)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\math.h)(0x6569B012)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\string.h)(0x6569B012)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdio.h)(0x6569B012)
F (../Core/Src/balance_control.c)(0x68771299)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_motor

-ID:/blue/my_program/app_uv/ARM/Packs/ARM/CMSIS/5.9.0/CMSIS/Core/Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F407xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F407xx

-o motor/balance_control.o -MD)
I (..\Core\Inc\balance_control.h)(0x6876690C)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x684904BC)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6875F214)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x684904BC)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f407xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x684904BA)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_version.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_compiler.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_armclang.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\mpu_armv7.h)(0x00000000)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x684904BA)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684904BC)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x684904BC)
I (..\Core\Inc\pid_controller.h)(0x68766889)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\math.h)(0x6569B012)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\string.h)(0x6569B012)
I (..\Core\Inc\attitude.h_.\Core\Inc\mpu6050.hu.\Core\Inc\i2c.h)(0x00000000)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdio.h)(0x6569B012)
F (../Core/Src/motor_control.c)(0x6877127D)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_motor

-ID:/blue/my_program/app_uv/ARM/Packs/ARM/CMSIS/5.9.0/CMSIS/Core/Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F407xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F407xx

-o motor/motor_control.o -MD)
I (..\Core\Inc\motor_control.h)(0x68766993)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x684904BC)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6875F214)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x684904BC)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f407xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x684904BA)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_version.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_compiler.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_armclang.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\mpu_armv7.h)(0x00000000)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x684904BA)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684904BC)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x684904BC)
I (..\Core\Inc\tim.h4.\Core\Inc\gpio.h)(0x00000000)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\math.h)(0x6569B012)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdio.h)(0x6569B012)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\string.h)(0x6569B012)
F (../Core/Src/balance_system.c)(0x6877130A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_motor

-ID:/blue/my_program/app_uv/ARM/Packs/ARM/CMSIS/5.9.0/CMSIS/Core/Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F407xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F407xx

-o motor/balance_system.o -MD)
I (..\Core\Inc\balance_system.h)(0x68766A45)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x684904BC)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6875F214)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x684904BC)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f407xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x684904BA)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_version.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_compiler.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_armclang.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\mpu_armv7.h)(0x00000000)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x684904BA)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684904BC)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x684904BC)
I (..\Core\Inc\pid_controller.h)(0x68766889)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\math.h)(0x6569B012)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\string.h)(0x6569B012)
I (..\Core\Inc\balance_control.h\.\Core\Inc\attitude.h)(0x00000000)
I (..\Core\Inc\mpu6050.ho.\Core\Inc\i2c.hn.\Core\Inc\motor_control.h)(0x00000000)
I (..\Core\Inc\tim.h5.\Core\Inc\gpio.h)(0x00000000)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdio.h)(0x6569B012)
F (../Core/Src/pid_tuner.c)(0x68766FE1)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_motor

-ID:/blue/my_program/app_uv/ARM/Packs/ARM/CMSIS/5.9.0/CMSIS/Core/Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F407xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F407xx

-o motor/pid_tuner.o -MD)
I (..\Core\Inc\main.hn.\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x00000000)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6875F214)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x684904BC)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f407xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x684904BA)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_version.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_compiler.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_armclang.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\mpu_armv7.h)(0x00000000)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x684904BA)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684904BC)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x684904BC)
I (..\Core\Inc\balance_system.hv.\Core\Inc\pid_controller.h)(0x00000000)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\math.h)(0x6569B012)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\string.h)(0x6569B012)
I (..\Core\Inc\balance_control.h\.\Core\Inc\attitude.h)(0x00000000)
I (..\Core\Inc\mpu6050.ho.\Core\Inc\i2c.hn.\Core\Inc\motor_control.h)(0x00000000)
I (..\Core\Inc\tim.h5.\Core\Inc\gpio.h)(0x00000000)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdio.h)(0x6569B012)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdlib.h)(0x6569B012)
F (../Core/Src/system_diagnostics.c)(0x68771263)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_motor

-ID:/blue/my_program/app_uv/ARM/Packs/ARM/CMSIS/5.9.0/CMSIS/Core/Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F407xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F407xx

-o motor/system_diagnostics.o -MD)
I (..\Core\Inc\system_diagnostics.h)(0x6877111B)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x684904BC)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6875F214)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x684904BC)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f407xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x684904BA)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_version.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_compiler.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_armclang.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\mpu_armv7.h)(0x00000000)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x684904BA)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684904BC)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x684904BC)
I (..\Core\Inc\mpu6050.hH.\Core\Inc\i2c.hm.\Core\Inc\motor_control.h)(0x00000000)
I (..\Core\Inc\tim.h5.\Core\Inc\gpio.h)(0x00000000)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\math.h)(0x6569B012)
I (..\Core\Inc\balance_system.hM.\Core\Inc\pid_controller.h)(0x00000000)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\string.h)(0x6569B012)
I (..\Core\Inc\balance_control.h\.\Core\Inc\attitude.h)(0x00000000)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdio.h)(0x6569B012)
I (..\Core\Inc\usart.h)(0x6875F212)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.c)(0x684904BC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_motor

-ID:/blue/my_program/app_uv/ARM/Packs/ARM/CMSIS/5.9.0/CMSIS/Core/Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F407xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F407xx

-o motor/stm32f4xx_hal_i2c.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x684904BC)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6875F214)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x684904BC)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f407xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x684904BA)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_version.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_compiler.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_armclang.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\mpu_armv7.h)(0x00000000)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x684904BA)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684904BC)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x684904BC)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.c)(0x684904BC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_motor

-ID:/blue/my_program/app_uv/ARM/Packs/ARM/CMSIS/5.9.0/CMSIS/Core/Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F407xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F407xx

-o motor/stm32f4xx_hal_i2c_ex.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x684904BC)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6875F214)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x684904BC)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f407xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x684904BA)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_version.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_compiler.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_armclang.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\mpu_armv7.h)(0x00000000)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x684904BA)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684904BC)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x684904BC)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c)(0x684904BC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_motor

-ID:/blue/my_program/app_uv/ARM/Packs/ARM/CMSIS/5.9.0/CMSIS/Core/Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F407xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F407xx

-o motor/stm32f4xx_hal_rcc.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x684904BC)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6875F214)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x684904BC)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f407xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x684904BA)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_version.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_compiler.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_armclang.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\mpu_armv7.h)(0x00000000)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x684904BA)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684904BC)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x684904BC)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c)(0x684904BC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_motor

-ID:/blue/my_program/app_uv/ARM/Packs/ARM/CMSIS/5.9.0/CMSIS/Core/Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F407xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F407xx

-o motor/stm32f4xx_hal_rcc_ex.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x684904BC)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6875F214)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x684904BC)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f407xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x684904BA)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_version.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_compiler.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_armclang.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\mpu_armv7.h)(0x00000000)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x684904BA)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684904BC)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x684904BC)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c)(0x684904BC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_motor

-ID:/blue/my_program/app_uv/ARM/Packs/ARM/CMSIS/5.9.0/CMSIS/Core/Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F407xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F407xx

-o motor/stm32f4xx_hal_flash.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x684904BC)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6875F214)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x684904BC)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f407xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x684904BA)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_version.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_compiler.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_armclang.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\mpu_armv7.h)(0x00000000)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x684904BA)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684904BC)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x684904BC)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c)(0x684904BC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_motor

-ID:/blue/my_program/app_uv/ARM/Packs/ARM/CMSIS/5.9.0/CMSIS/Core/Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F407xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F407xx

-o motor/stm32f4xx_hal_flash_ex.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x684904BC)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6875F214)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x684904BC)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f407xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x684904BA)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_version.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_compiler.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_armclang.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\mpu_armv7.h)(0x00000000)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x684904BA)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684904BC)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x684904BC)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c)(0x684904BC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_motor

-ID:/blue/my_program/app_uv/ARM/Packs/ARM/CMSIS/5.9.0/CMSIS/Core/Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F407xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F407xx

-o motor/stm32f4xx_hal_flash_ramfunc.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x684904BC)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6875F214)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x684904BC)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f407xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x684904BA)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_version.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_compiler.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_armclang.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\mpu_armv7.h)(0x00000000)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x684904BA)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684904BC)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x684904BC)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c)(0x684904BC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_motor

-ID:/blue/my_program/app_uv/ARM/Packs/ARM/CMSIS/5.9.0/CMSIS/Core/Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F407xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F407xx

-o motor/stm32f4xx_hal_gpio.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x684904BC)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6875F214)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x684904BC)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f407xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x684904BA)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_version.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_compiler.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_armclang.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\mpu_armv7.h)(0x00000000)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x684904BA)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684904BC)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x684904BC)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c)(0x684904BC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_motor

-ID:/blue/my_program/app_uv/ARM/Packs/ARM/CMSIS/5.9.0/CMSIS/Core/Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F407xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F407xx

-o motor/stm32f4xx_hal_dma_ex.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x684904BC)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6875F214)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x684904BC)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f407xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x684904BA)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_version.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_compiler.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_armclang.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\mpu_armv7.h)(0x00000000)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x684904BA)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684904BC)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x684904BC)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c)(0x684904BC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_motor

-ID:/blue/my_program/app_uv/ARM/Packs/ARM/CMSIS/5.9.0/CMSIS/Core/Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F407xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F407xx

-o motor/stm32f4xx_hal_dma.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x684904BC)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6875F214)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x684904BC)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f407xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x684904BA)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_version.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_compiler.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_armclang.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\mpu_armv7.h)(0x00000000)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x684904BA)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684904BC)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x684904BC)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c)(0x684904BC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_motor

-ID:/blue/my_program/app_uv/ARM/Packs/ARM/CMSIS/5.9.0/CMSIS/Core/Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F407xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F407xx

-o motor/stm32f4xx_hal_pwr.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x684904BC)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6875F214)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x684904BC)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f407xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x684904BA)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_version.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_compiler.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_armclang.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\mpu_armv7.h)(0x00000000)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x684904BA)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684904BC)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x684904BC)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c)(0x684904BC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_motor

-ID:/blue/my_program/app_uv/ARM/Packs/ARM/CMSIS/5.9.0/CMSIS/Core/Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F407xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F407xx

-o motor/stm32f4xx_hal_pwr_ex.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x684904BC)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6875F214)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x684904BC)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f407xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x684904BA)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_version.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_compiler.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_armclang.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\mpu_armv7.h)(0x00000000)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x684904BA)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684904BC)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x684904BC)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c)(0x684904BC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_motor

-ID:/blue/my_program/app_uv/ARM/Packs/ARM/CMSIS/5.9.0/CMSIS/Core/Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F407xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F407xx

-o motor/stm32f4xx_hal_cortex.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x684904BC)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6875F214)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x684904BC)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f407xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x684904BA)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_version.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_compiler.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_armclang.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\mpu_armv7.h)(0x00000000)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x684904BA)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684904BC)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x684904BC)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c)(0x684904BC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_motor

-ID:/blue/my_program/app_uv/ARM/Packs/ARM/CMSIS/5.9.0/CMSIS/Core/Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F407xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F407xx

-o motor/stm32f4xx_hal.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x684904BC)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6875F214)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x684904BC)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f407xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x684904BA)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_version.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_compiler.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_armclang.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\mpu_armv7.h)(0x00000000)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x684904BA)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684904BC)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x684904BC)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c)(0x684904BC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_motor

-ID:/blue/my_program/app_uv/ARM/Packs/ARM/CMSIS/5.9.0/CMSIS/Core/Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F407xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F407xx

-o motor/stm32f4xx_hal_exti.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x684904BC)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6875F214)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x684904BC)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f407xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x684904BA)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_version.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_compiler.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_armclang.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\mpu_armv7.h)(0x00000000)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x684904BA)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684904BC)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x684904BC)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c)(0x684904BC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_motor

-ID:/blue/my_program/app_uv/ARM/Packs/ARM/CMSIS/5.9.0/CMSIS/Core/Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F407xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F407xx

-o motor/stm32f4xx_hal_tim.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x684904BC)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6875F214)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x684904BC)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f407xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x684904BA)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_version.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_compiler.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_armclang.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\mpu_armv7.h)(0x00000000)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x684904BA)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684904BC)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x684904BC)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c)(0x684904BC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_motor

-ID:/blue/my_program/app_uv/ARM/Packs/ARM/CMSIS/5.9.0/CMSIS/Core/Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F407xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F407xx

-o motor/stm32f4xx_hal_tim_ex.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x684904BC)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6875F214)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x684904BC)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f407xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x684904BA)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_version.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_compiler.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_armclang.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\mpu_armv7.h)(0x00000000)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x684904BA)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684904BC)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x684904BC)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c)(0x684904BC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_motor

-ID:/blue/my_program/app_uv/ARM/Packs/ARM/CMSIS/5.9.0/CMSIS/Core/Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F407xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F407xx

-o motor/stm32f4xx_hal_uart.o -MD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x684904BC)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6875F214)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x684904BC)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f407xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x684904BA)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_version.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_compiler.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_armclang.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\mpu_armv7.h)(0x00000000)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x684904BA)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684904BC)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x684904BC)
F (../Core/Src/system_stm32f4xx.c)(0x684904BA)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include

-I./RTE/_motor

-ID:/blue/my_program/app_uv/ARM/Packs/ARM/CMSIS/5.9.0/CMSIS/Core/Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F407xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F407xx

-o motor/system_stm32f4xx.o -MD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f407xx.h)(0x684904BA)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x684904BA)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_version.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_compiler.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\cmsis_armclang.h)(0x00000000)
I (C:\Users\<USER>\Desktop\电赛\motor无编码器 (2)\motor无编码器\motor无编码器\Drivers\CMSIS\Include\mpu_armv7.h)(0x00000000)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x684904BA)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x684904BC)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6875F214)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684904BC)
I (D:\blue\my_program\app_uv\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x684904BC)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x684904BC)
