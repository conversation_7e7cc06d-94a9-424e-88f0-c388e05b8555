# STM32F4电机驱动实现指南

## 1. 文档信息
- **版本**: v1.0
- **创建日期**: 2025-01-15
- **负责人**: <PERSON> (工程师)
- **项目名称**: STM32F4双电机驱动测试实现

## 2. 代码实现说明

### 2.1 实现概述
本实现在STM32F4的main.c文件中添加了电机驱动控制代码，实现上电后双电机自动正转的功能。代码遵循STM32 HAL库编程规范，放置在USER CODE区域以避免被CubeMX覆盖。

### 2.2 核心实现代码
```c
/* USER CODE BEGIN 2 */

// 电机驱动初始化和启动
// 启动TIM1的PWM输出 - 左电机(CH1)和右电机(CH2)
HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_1);  // 左电机PWM (PE9)
HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_2);  // 右电机PWM (PE11)

// 设置电机方向为正转
// 左电机A正转: AIN1=1, AIN2=0
HAL_GPIO_WritePin(AIN1_GPIO_Port, AIN1_Pin, GPIO_PIN_SET);    // PE12 = 1
HAL_GPIO_WritePin(AIN2_GPIO_Port, AIN2_Pin, GPIO_PIN_RESET);  // PE14 = 0

// 右电机B正转: BIN1=1, BIN2=0  
HAL_GPIO_WritePin(BIN1_GPIO_Port, BIN1_Pin, GPIO_PIN_SET);    // PE8 = 1
HAL_GPIO_WritePin(BIN2_GPIO_Port, BIN2_Pin, GPIO_PIN_RESET);  // PE10 = 0

// 设置PWM占空比为50% (500/1000)
__HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, 500);  // 左电机速度50%
__HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_2, 500);  // 右电机速度50%

// 短暂延时确保设置生效
HAL_Delay(100);

/* USER CODE END 2 */
```

### 2.3 代码工作原理

#### 2.3.1 PWM启动
- `HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_1)`: 启动TIM1通道1的PWM输出(PE9)
- `HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_2)`: 启动TIM1通道2的PWM输出(PE11)
- PWM频率: 21kHz (168MHz/8/1000)
- PWM分辨率: 1000级 (0.1%精度)

#### 2.3.2 方向控制
根据TB6612真值表设置电机方向：
- **正转**: IN1=1, IN2=0
- **反转**: IN1=0, IN2=1  
- **停止**: IN1=0, IN2=0
- **刹车**: IN1=1, IN2=1

#### 2.3.3 速度控制
- 使用`__HAL_TIM_SET_COMPARE()`宏设置PWM占空比
- 占空比范围: 0-999 (对应0%-99.9%)
- 当前设置: 500 (50%占空比)

## 3. 硬件连接验证

### 3.1 引脚连接检查表
| STM32F4引脚 | TB6612引脚 | 信号名称 | 功能描述 | 代码中的宏 |
|-------------|------------|----------|----------|------------|
| PE9         | PWMA       | TIM1_CH1 | 左电机PWM | - |
| PE11        | PWMB       | TIM1_CH2 | 右电机PWM | - |
| PE12        | AIN1       | GPIO     | 左电机方向1 | AIN1_Pin |
| PE14        | AIN2       | GPIO     | 左电机方向2 | AIN2_Pin |
| PE8         | BIN1       | GPIO     | 右电机方向1 | BIN1_Pin |
| PE10        | BIN2       | GPIO     | 右电机方向2 | BIN2_Pin |
| 3.3V        | VCC        | 电源     | 逻辑电源 | - |
| 3.3V        | STBY       | 控制     | 待机控制 | - |
| GND         | GND        | 地线     | 共地 | - |

### 3.2 信号时序
```
上电序列:
1. 系统初始化 (HAL_Init, 时钟配置)
2. 外设初始化 (GPIO, TIM1)
3. PWM启动
4. 方向设置
5. 速度设置
6. 延时稳定
7. 进入主循环
```

## 4. 编译和下载指南

### 4.1 编译环境
- **IDE**: MDK-ARM (Keil uVision)
- **编译器**: ARM Compiler 5/6
- **目标芯片**: STM32F407VGT6
- **项目文件**: motor.uvprojx

### 4.2 编译步骤
1. 打开MDK-ARM目录下的motor.uvprojx项目文件
2. 选择目标配置 (Debug/Release)
3. 点击编译按钮或按F7
4. 检查编译输出，确保无错误和警告

### 4.3 下载步骤
1. 连接ST-Link调试器到STM32F4开发板
2. 在Keil中选择下载配置
3. 点击下载按钮或按F8
4. 等待下载完成

## 5. 测试步骤

### 5.1 功能测试
1. **上电测试**:
   - 给STM32F4开发板上电
   - 观察双电机是否同时开始转动
   - 确认转动方向为正转

2. **PWM信号测试**:
   - 使用示波器连接PE9引脚 (左电机PWM)
   - 测量PWM频率应为21kHz ±1%
   - 测量PWM占空比应为50% ±1%
   - 重复测试PE11引脚 (右电机PWM)

3. **GPIO信号测试**:
   - 使用万用表或逻辑分析仪测量GPIO状态
   - PE12 (AIN1) = 3.3V (高电平)
   - PE14 (AIN2) = 0V (低电平)
   - PE8 (BIN1) = 3.3V (高电平)
   - PE10 (BIN2) = 0V (低电平)

### 5.2 性能测试
1. **电机转速测试**:
   - 使用转速计测量电机实际转速
   - 验证双电机转速基本一致
   - 记录转速数据

2. **电流测试**:
   - 测量电机工作电流
   - 确保电流在TB6612规格范围内
   - 检查是否有异常电流波动

### 5.3 稳定性测试
1. **长时间运行测试**:
   - 连续运行30分钟
   - 观察电机是否稳定转动
   - 检查温度是否正常

2. **重复上电测试**:
   - 多次断电重启
   - 验证每次上电都能正常启动
   - 确保功能一致性

## 6. 故障排除指南

### 6.1 常见问题及解决方案

#### 问题1: 电机不转动
**可能原因**:
- 硬件连接错误
- 电源供应不足
- TB6612未正确使能

**解决方案**:
1. 检查所有硬件连接
2. 确认电源电压和电流充足
3. 检查STBY引脚是否连接到3.3V
4. 使用万用表测量各引脚电压

#### 问题2: 只有一个电机转动
**可能原因**:
- PWM通道配置错误
- GPIO引脚连接问题
- 电机或驱动芯片损坏

**解决方案**:
1. 检查TIM1的两个PWM通道配置
2. 验证所有GPIO连接
3. 交换电机测试是否为硬件问题

#### 问题3: 电机转向错误
**可能原因**:
- 方向控制引脚接反
- 电机接线错误
- 代码中方向设置错误

**解决方案**:
1. 检查AIN1/AIN2和BIN1/BIN2连接
2. 确认电机接线正确
3. 修改代码中的GPIO设置

#### 问题4: 编译错误
**可能原因**:
- 头文件缺失
- 宏定义未找到
- HAL库版本不兼容

**解决方案**:
1. 确认所有必要头文件已包含
2. 检查main.h中的宏定义
3. 验证HAL库版本兼容性

### 6.2 调试技巧
1. **使用调试器**:
   - 设置断点检查变量值
   - 单步执行验证代码流程
   - 监视寄存器状态

2. **添加调试输出**:
   - 使用串口输出调试信息
   - 添加LED指示状态
   - 记录关键变量值

3. **硬件调试**:
   - 使用示波器观察信号
   - 用万用表测量电压
   - 检查连接的连续性

## 7. 扩展建议

### 7.1 功能扩展
1. **速度调节**:
   - 添加按键或旋钮控制速度
   - 实现PWM占空比动态调整
   - 支持速度预设档位

2. **方向控制**:
   - 添加方向切换功能
   - 实现正转/反转/停止控制
   - 支持刹车功能

3. **编码器反馈**:
   - 启用TIM3/TIM4编码器功能
   - 实现速度反馈
   - 添加位置控制

### 7.2 代码优化
1. **模块化设计**:
   - 将电机控制封装为独立函数
   - 创建电机控制结构体
   - 实现统一的电机接口

2. **错误处理**:
   - 添加HAL函数返回值检查
   - 实现错误状态指示
   - 增加异常恢复机制

3. **性能优化**:
   - 使用定时器中断更新PWM
   - 实现平滑启动/停止
   - 优化CPU占用率

## 8. 参考资料

### 8.1 技术文档
- STM32F407 Reference Manual
- STM32F4xx HAL Driver User Manual
- TB6612FNG Datasheet
- STM32CubeMX User Manual

### 8.2 相关代码
- HAL_TIM_PWM_Start() 函数文档
- HAL_GPIO_WritePin() 函数文档
- __HAL_TIM_SET_COMPARE() 宏定义

### 8.3 开发工具
- STM32CubeMX 配置工具
- MDK-ARM 开发环境
- ST-Link 调试器
- 示波器和万用表