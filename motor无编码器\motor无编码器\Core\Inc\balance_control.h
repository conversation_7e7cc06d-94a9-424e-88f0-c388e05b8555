/**
 ******************************************************************************
 * @file    balance_control.h
 * @brief   平衡控制器头文件 - 基于PID的平衡车控制系统
 * <AUTHOR> (工程师)
 * @version v1.0
 * @date    2025-01-15
 ******************************************************************************
 * @attention
 * 
 * 本文件实现了平衡车的核心控制功能，包括：
 * - 基于PID的角度控制
 * - 多级控制策略
 * - 安全保护机制
 * - 实时状态监控
 * - 参数在线调节
 * 
 ******************************************************************************
 */

#ifndef __BALANCE_CONTROL_H
#define __BALANCE_CONTROL_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "pid_controller.h"
#include "attitude.h"
#include "mpu6050.h"
#include <math.h>

/* Exported types ------------------------------------------------------------*/

/**
 * @brief 平衡控制器状态枚举
 */
typedef enum {
    BALANCE_STATE_DISABLED = 0,    /**< 禁用状态 */
    BALANCE_STATE_INITIALIZING = 1, /**< 初始化状态 */
    BALANCE_STATE_CALIBRATING = 2,  /**< 校准状态 */
    BALANCE_STATE_READY = 3,        /**< 就绪状态 */
    BALANCE_STATE_RUNNING = 4,      /**< 运行状态 */
    BALANCE_STATE_EMERGENCY = 5,    /**< 紧急停止状态 */
    BALANCE_STATE_ERROR = 6         /**< 错误状态 */
} Balance_State_t;

/**
 * @brief 控制模式枚举
 */
typedef enum {
    BALANCE_MODE_ANGLE_ONLY = 0,    /**< 仅角度控制 */
    BALANCE_MODE_ANGLE_SPEED = 1,   /**< 角度+速度控制 */
    BALANCE_MODE_ADAPTIVE = 2       /**< 自适应控制 */
} Balance_Mode_t;

/**
 * @brief 平衡控制器配置结构体
 */
typedef struct {
    float target_angle;             /**< 目标平衡角度 (度) */
    float angle_tolerance;          /**< 角度容差范围 (度) */
    float max_tilt_angle;           /**< 最大允许倾斜角度 (度) */
    float max_angular_velocity;     /**< 最大角速度限制 (度/秒) */
    float control_frequency;        /**< 控制频率 (Hz) */
    float balance_threshold;        /**< 平衡判定阈值 (度) */
    uint32_t balance_stable_time;   /**< 平衡稳定时间 (ms) */
    uint8_t enable_multi_level;     /**< 多级控制使能 */
    uint8_t enable_speed_compensation; /**< 速度补偿使能 */
    uint8_t enable_auto_recovery;   /**< 自动恢复使能 */
} Balance_Config_t;

/**
 * @brief 平衡控制器状态信息结构体
 */
typedef struct {
    float current_angle;            /**< 当前角度 (度) */
    float angle_error;              /**< 角度误差 (度) */
    float angular_velocity;         /**< 当前角速度 (度/秒) */
    float control_output;           /**< 控制输出 */
    uint32_t control_cycles;        /**< 控制周期计数 */
    uint32_t balance_time;          /**< 连续平衡时间 (ms) */
    uint32_t last_balance_time;     /**< 上次平衡时间戳 */
    uint8_t is_balanced;            /**< 是否处于平衡状态 */
    uint8_t emergency_stop;         /**< 紧急停止标志 */
    uint8_t fault_code;             /**< 故障代码 */
} Balance_Status_t;

/**
 * @brief 多级控制参数结构体
 */
typedef struct {
    float coarse_threshold;         /**< 粗调阈值 (度) */
    float fine_threshold;           /**< 精调阈值 (度) */
    PID_Params_t coarse_pid;        /**< 粗调PID参数 */
    PID_Params_t fine_pid;          /**< 精调PID参数 */
    PID_Params_t stable_pid;        /**< 稳态PID参数 */
} MultiLevel_Params_t;

/**
 * @brief 平衡控制器主结构体
 */
typedef struct {
    Balance_State_t state;          /**< 控制器状态 */
    Balance_Mode_t mode;            /**< 控制模式 */
    Balance_Config_t config;        /**< 配置参数 */
    Balance_Status_t status;        /**< 状态信息 */
    
    PID_Controller_t angle_pid;     /**< 角度环PID控制器 */
    MultiLevel_Params_t multi_level; /**< 多级控制参数 */
    
    Attitude_Data *attitude_data;   /**< 姿态数据指针 */
    MPU6050_Data *sensor_data;      /**< 传感器数据指针 */
    
    uint32_t last_update_time;      /**< 上次更新时间 */
    uint32_t start_time;            /**< 启动时间 */
} Balance_Controller_t;

/* Exported constants --------------------------------------------------------*/

/** @defgroup Balance_Default_Config 默认配置参数
 * @{
 */
#define BALANCE_DEFAULT_TARGET_ANGLE        0.0f    /**< 默认目标角度 */
#define BALANCE_DEFAULT_ANGLE_TOLERANCE     2.0f    /**< 默认角度容差 */
#define BALANCE_DEFAULT_MAX_TILT_ANGLE      45.0f   /**< 默认最大倾斜角度 */
#define BALANCE_DEFAULT_MAX_ANGULAR_VEL     360.0f  /**< 默认最大角速度 */
#define BALANCE_DEFAULT_CONTROL_FREQ        200.0f  /**< 默认控制频率 */
#define BALANCE_DEFAULT_BALANCE_THRESHOLD   1.0f    /**< 默认平衡阈值 */
#define BALANCE_DEFAULT_STABLE_TIME         1000    /**< 默认稳定时间 */
/**
 * @}
 */

/** @defgroup Balance_PID_Params 默认PID参数
 * @{
 */
#define BALANCE_DEFAULT_KP                  15.0f   /**< 默认比例系数 */
#define BALANCE_DEFAULT_KI                  0.5f    /**< 默认积分系数 */
#define BALANCE_DEFAULT_KD                  0.8f    /**< 默认微分系数 */
#define BALANCE_DEFAULT_MAX_OUTPUT          800.0f  /**< 默认最大输出 */
#define BALANCE_DEFAULT_MIN_OUTPUT          -800.0f /**< 默认最小输出 */
/**
 * @}
 */

/** @defgroup Balance_Error_Codes 错误代码
 * @{
 */
#define BALANCE_ERROR_NONE                  0       /**< 无错误 */
#define BALANCE_ERROR_SENSOR_TIMEOUT        1       /**< 传感器超时 */
#define BALANCE_ERROR_ANGLE_OVERFLOW        2       /**< 角度超限 */
#define BALANCE_ERROR_ANGULAR_VEL_OVERFLOW  3       /**< 角速度超限 */
#define BALANCE_ERROR_PID_SATURATION        4       /**< PID饱和 */
#define BALANCE_ERROR_SYSTEM_OVERLOAD       5       /**< 系统过载 */
#define BALANCE_ERROR_INVALID_PARAMETER     6       /**< 参数无效 */
/**
 * @}
 */

/* Exported macro ------------------------------------------------------------*/

/**
 * @brief 角度转弧度
 */
#define BALANCE_DEG_TO_RAD(deg) ((deg) * 0.017453292519943295f)

/**
 * @brief 弧度转角度
 */
#define BALANCE_RAD_TO_DEG(rad) ((rad) * 57.29577951308232f)

/**
 * @brief 检查角度是否在安全范围内
 */
#define BALANCE_IS_ANGLE_SAFE(angle, max_angle) \
    (fabsf(angle) <= fabsf(max_angle))

/**
 * @brief 检查平衡控制器是否就绪
 */
#define BALANCE_IS_READY(balance) \
    ((balance) != NULL && (balance)->state >= BALANCE_STATE_READY)

/* Exported functions prototypes ---------------------------------------------*/

/** @defgroup Balance_Initialization_Functions 初始化函数
 * @{
 */
HAL_StatusTypeDef Balance_Init(Balance_Controller_t *balance);
HAL_StatusTypeDef Balance_SetConfig(Balance_Controller_t *balance, const Balance_Config_t *config);
HAL_StatusTypeDef Balance_SetDefaultConfig(Balance_Controller_t *balance);
void Balance_AttachSensorData(Balance_Controller_t *balance, MPU6050_Data *sensor_data, Attitude_Data *attitude_data);
HAL_StatusTypeDef Balance_Calibrate(Balance_Controller_t *balance);
/**
 * @}
 */

/** @defgroup Balance_Control_Functions 控制函数
 * @{
 */
HAL_StatusTypeDef Balance_Update(Balance_Controller_t *balance);
void Balance_Start(Balance_Controller_t *balance);
void Balance_Stop(Balance_Controller_t *balance);
void Balance_EmergencyStop(Balance_Controller_t *balance);
void Balance_Reset(Balance_Controller_t *balance);
/**
 * @}
 */

/** @defgroup Balance_Parameter_Functions 参数调节函数
 * @{
 */
void Balance_SetTargetAngle(Balance_Controller_t *balance, float target_angle);
void Balance_SetPIDParams(Balance_Controller_t *balance, float kp, float ki, float kd);
void Balance_SetControlMode(Balance_Controller_t *balance, Balance_Mode_t mode);
void Balance_SetMultiLevelParams(Balance_Controller_t *balance, const MultiLevel_Params_t *params);
/**
 * @}
 */

/** @defgroup Balance_Status_Functions 状态查询函数
 * @{
 */
Balance_Status_t* Balance_GetStatus(Balance_Controller_t *balance);
Balance_State_t Balance_GetState(const Balance_Controller_t *balance);
uint8_t Balance_IsBalanced(const Balance_Controller_t *balance);
float Balance_GetControlOutput(const Balance_Controller_t *balance);
float Balance_GetCurrentAngle(const Balance_Controller_t *balance);
uint32_t Balance_GetBalanceTime(const Balance_Controller_t *balance);
/**
 * @}
 */

/** @defgroup Balance_Utility_Functions 工具函数
 * @{
 */
void Balance_PrintStatus(const Balance_Controller_t *balance);
HAL_StatusTypeDef Balance_SelfTest(Balance_Controller_t *balance);
const char* Balance_GetStateString(Balance_State_t state);
/**
 * @}
 */

#ifdef __cplusplus
}
#endif

#endif /* __BALANCE_CONTROL_H */

/**
 * @}
 */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
