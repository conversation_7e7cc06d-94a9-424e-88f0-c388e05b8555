# 🚀 PID平衡控制器使用指南

## 📋 系统概述

您的STM32F4平衡车已经集成了完整的PID控制系统，能够实现真正的自平衡功能！

### ✅ 已实现的核心功能
- **完整的PID控制算法** (位置式 + 增量式)
- **智能平衡控制** (±2°精度)
- **多级控制策略** (根据误差自动调节)
- **安全保护机制** (角度限制 + 紧急停止)
- **实时参数调节** (手动 + 自动优化)
- **性能监控** (200Hz控制频率)

## 🔌 硬件连接确认

### MPU6050传感器 (I2C1)
```
MPU6050    →    STM32F407
VCC        →    3.3V
GND        →    GND
SCL        →    PB6
SDA        →    PB7
```

### TB6612FNG电机驱动
```
TB6612     →    STM32F407
PWMA       →    PE9  (左电机PWM)
PWMB       →    PE11 (右电机PWM)
AIN1       →    PE7  (左电机方向1)
AIN2       →    PE8  (左电机方向2)
BIN1       →    PE9  (右电机方向1)
BIN2       →    PE10 (右电机方向2)
```

### 调试串口
```
STM32F407  →    USB转串口
PA9 (TX)   →    RX
PA10 (RX)  →    TX
```

## 🚀 快速启动步骤

### 1. 编译和下载
1. 在Keil中打开项目文件 `MDK-ARM/motor.uvprojx`
2. 编译项目 (F7键)
3. 连接ST-Link并下载程序 (F8键)

### 2. 串口监控
1. 打开串口调试助手
2. 设置波特率：**115200**
3. 连接对应的COM口

### 3. 系统启动
上电后您会看到以下启动序列：
```
=== PID Balance Car System Starting ===
Initializing complete balance control system...
SUCCESS: Balance system initialized!

Starting system calibration...
Please keep the balance car upright and stationary!
SUCCESS: System calibration completed!

Setting initial PID parameters...
=== System Ready ===
SUCCESS: Balance system started!
```

## 📊 实时数据监控

系统会输出实时控制数据，格式如下：
```
T:12345,S:3,A:1.23,E:-0.45,O:123.4,B:1,L:456,R:456
```

**数据含义**：
- **T**: 时间戳 (毫秒)
- **S**: 系统状态 (3=运行中)
- **A**: 当前角度 (度)
- **E**: 角度误差 (度)
- **O**: PID控制输出
- **B**: 是否平衡 (1=平衡, 0=不平衡)
- **L/R**: 左右电机PWM值

## ⚙️ PID参数调节详解

### 当前默认参数
```c
Kp = 15.0  // 比例系数
Ki = 0.5   // 积分系数
Kd = 0.8   // 微分系数
```

### 参数作用说明

#### 🎯 Kp (比例系数) - 最重要
- **作用**: 根据当前角度误差产生控制力
- **效果**: 
  - 太小 → 响应慢，无法平衡
  - 太大 → 振荡剧烈
  - 合适 → 快速响应，稳定控制

#### 🔄 Ki (积分系数) - 消除稳态误差
- **作用**: 消除长期存在的角度偏差
- **效果**:
  - 太小 → 有稳态偏差
  - 太大 → 超调，不稳定
  - 合适 → 无偏差，稳定

#### ⚡ Kd (微分系数) - 提供阻尼
- **作用**: 预测角度变化趋势，提前制动
- **效果**:
  - 太小 → 超调，振荡
  - 太大 → 响应慢，对噪声敏感
  - 合适 → 快速稳定，无超调

## 🔧 参数调节方法

### 方法1: 手动调节 (推荐新手)

#### Step 1: 调节Kp值
```c
// 在main.c第129行修改Kp值
Balance_System_SetPIDParams(&g_balance_system, 20.0f, 0.5f, 0.8f);
//                                              ↑修改这里
```

**调节策略**：
- 从10.0开始测试
- 如果倾倒 → 增大Kp (每次+5)
- 如果振荡 → 减小Kp (每次-2)
- 目标：能短暂保持平衡

#### Step 2: 调节Kd值
```c
// 在确定Kp后调节Kd
Balance_System_SetPIDParams(&g_balance_system, 15.0f, 0.5f, 1.2f);
//                                                           ↑修改这里
```

**调节策略**：
- 从0.5开始测试
- 如果振荡 → 增大Kd (每次+0.3)
- 如果响应慢 → 减小Kd (每次-0.2)
- 目标：稳定平衡，无振荡

#### Step 3: 调节Ki值
```c
// 最后微调Ki值
Balance_System_SetPIDParams(&g_balance_system, 15.0f, 0.8f, 0.8f);
//                                                      ↑修改这里
```

**调节策略**：
- 从0.3开始测试
- 如果有偏差 → 增大Ki (每次+0.2)
- 如果不稳定 → 减小Ki (每次-0.1)
- 目标：无稳态误差

### 方法2: 自动调节 (推荐高级用户)

系统内置了自动参数调节功能：

```c
// 在main.c中添加这行代码 (替换手动设置)
Balance_System_TunePIDParams(&g_balance_system);
```

自动调节会：
1. 测试多组Kp值
2. 评估控制效果
3. 选择最优参数组合
4. 自动应用最佳参数

## 🎯 不同场景的推荐参数

### 轻量级平衡车 (< 1kg)
```c
Balance_System_SetPIDParams(&g_balance_system, 12.0f, 0.3f, 0.6f);
```

### 标准平衡车 (1-2kg)
```c
Balance_System_SetPIDParams(&g_balance_system, 15.0f, 0.5f, 0.8f);
```

### 重型平衡车 (> 2kg)
```c
Balance_System_SetPIDParams(&g_balance_system, 20.0f, 0.8f, 1.2f);
```

### 高精度控制 (竞赛用)
```c
Balance_System_SetPIDParams(&g_balance_system, 18.0f, 0.6f, 1.0f);
```

## 🔍 故障排除

### 问题1: 平衡车立即倾倒
**原因**: Kp值太小
**解决**: 增大Kp到20-25
```c
Balance_System_SetPIDParams(&g_balance_system, 25.0f, 0.5f, 0.8f);
```

### 问题2: 剧烈左右摆动
**原因**: Kp值太大或Kd不足
**解决**: 减小Kp或增大Kd
```c
Balance_System_SetPIDParams(&g_balance_system, 10.0f, 0.5f, 1.5f);
```

### 问题3: 缓慢振荡
**原因**: Ki值太大
**解决**: 减小Ki值
```c
Balance_System_SetPIDParams(&g_balance_system, 15.0f, 0.2f, 0.8f);
```

### 问题4: 有角度偏差
**原因**: Ki值太小或需要重新校准
**解决**: 增大Ki或重新校准
```c
// 方法1: 增大Ki
Balance_System_SetPIDParams(&g_balance_system, 15.0f, 0.8f, 0.8f);

// 方法2: 重新校准 (重启系统)
```

### 问题5: 电机不转
**检查项**:
1. 电机供电是否正常 (6-12V)
2. TB6612连接是否正确
3. PWM信号是否输出 (观察串口L/R数值)

### 问题6: 传感器无数据
**检查项**:
1. MPU6050供电 (3.3V)
2. I2C连接 (SCL-PB6, SDA-PB7)
3. 是否需要上拉电阻 (4.7kΩ)

## 📈 性能优化技巧

### 1. 多级控制策略 (已内置)
系统会根据角度误差自动调节参数：
- 大误差 (>10°): 快速响应
- 中误差 (2-10°): 平衡控制  
- 小误差 (<2°): 精确控制

### 2. 积分分离 (已内置)
大误差时自动禁用积分项，防止积分饱和

### 3. 微分先行 (已内置)
对测量值而非误差进行微分，减少噪声影响

### 4. 安全保护 (已内置)
- 角度限制: 超过45°自动停止
- 紧急停止: 异常情况立即停止
- 过载保护: 防止电机损坏

## 🎊 成功标志

当您看到以下现象时，说明PID控制器工作正常：

✅ **串口输出稳定数据** - 无错误信息  
✅ **平衡车自主直立** - 无需支撑  
✅ **快速恢复平衡** - 推动后1秒内恢复  
✅ **无明显振荡** - 动作平滑自然  
✅ **长时间稳定** - 可连续平衡30秒以上  

## 📞 技术支持

如需更多帮助，请查看：
- `docs/development/PID_Parameter_Tuning_Guide_v1.0.md` - 详细调节指南
- `docs/development/Project_Summary_v1.0.md` - 技术总结
- `README.md` - 项目说明

---

**恭喜！您现在拥有了一个完整的PID平衡控制系统！** 🎉

**记住**: 参数调节需要耐心，从Kp开始，逐步优化，最终一定能实现完美的平衡控制！
