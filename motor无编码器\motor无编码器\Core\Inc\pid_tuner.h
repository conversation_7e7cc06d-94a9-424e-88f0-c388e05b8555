/**
 ******************************************************************************
 * @file    pid_tuner.h
 * @brief   PID参数实时调节工具头文件
 * <AUTHOR> (工程师)
 * @version v1.0
 * @date    2025-01-15
 ******************************************************************************
 * @attention
 * 
 * 本文件实现了PID参数的实时调节功能，包括：
 * - 串口命令解析
 * - 实时参数修改
 * - 参数保存和恢复
 * - 自动调节算法
 * - 性能评估
 * 
 ******************************************************************************
 */

#ifndef __PID_TUNER_H
#define __PID_TUNER_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "balance_system.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>

/* Exported types ------------------------------------------------------------*/

/**
 * @brief 调节模式枚举
 */
typedef enum {
    TUNER_MODE_MANUAL = 0,    /**< 手动调节模式 */
    TUNER_MODE_AUTO = 1,      /**< 自动调节模式 */
    TUNER_MODE_STEP = 2       /**< 阶跃响应测试模式 */
} PID_Tuner_Mode_t;

/**
 * @brief 参数组合结构体
 */
typedef struct {
    float kp;                 /**< 比例系数 */
    float ki;                 /**< 积分系数 */
    float kd;                 /**< 微分系数 */
    float performance;        /**< 性能评分 */
    uint32_t test_time;       /**< 测试时间 */
} PID_Params_Set_t;

/**
 * @brief PID调节器主结构体
 */
typedef struct {
    PID_Tuner_Mode_t mode;           /**< 调节模式 */
    Balance_System_t *system;        /**< 平衡系统指针 */
    
    PID_Params_Set_t current_params; /**< 当前参数 */
    PID_Params_Set_t best_params;    /**< 最佳参数 */
    PID_Params_Set_t backup_params;  /**< 备份参数 */
    
    uint8_t auto_tuning_active;      /**< 自动调节激活标志 */
    uint32_t tuning_start_time;      /**< 调节开始时间 */
    uint32_t test_duration;          /**< 测试持续时间 */
    
    char command_buffer[64];         /**< 命令缓冲区 */
    uint8_t command_ready;           /**< 命令就绪标志 */
} PID_Tuner_t;

/* Exported constants --------------------------------------------------------*/

/** @defgroup Tuner_Commands 调节命令
 * @{
 */
#define CMD_SET_KP          "kp"     /**< 设置Kp命令 */
#define CMD_SET_KI          "ki"     /**< 设置Ki命令 */
#define CMD_SET_KD          "kd"     /**< 设置Kd命令 */
#define CMD_SET_ALL         "pid"    /**< 设置所有参数命令 */
#define CMD_GET_PARAMS      "get"    /**< 获取参数命令 */
#define CMD_SAVE_PARAMS     "save"   /**< 保存参数命令 */
#define CMD_LOAD_PARAMS     "load"   /**< 加载参数命令 */
#define CMD_AUTO_TUNE       "auto"   /**< 自动调节命令 */
#define CMD_RESET_PARAMS    "reset"  /**< 重置参数命令 */
#define CMD_HELP            "help"   /**< 帮助命令 */
/**
 * @}
 */

/** @defgroup Tuner_Default_Params 默认参数
 * @{
 */
#define TUNER_DEFAULT_TEST_TIME     5000    /**< 默认测试时间 (ms) */
#define TUNER_MIN_KP                1.0f    /**< 最小Kp值 */
#define TUNER_MAX_KP                50.0f   /**< 最大Kp值 */
#define TUNER_MIN_KI                0.0f    /**< 最小Ki值 */
#define TUNER_MAX_KI                5.0f    /**< 最大Ki值 */
#define TUNER_MIN_KD                0.0f    /**< 最小Kd值 */
#define TUNER_MAX_KD                5.0f    /**< 最大Kd值 */
/**
 * @}
 */

/* Exported macro ------------------------------------------------------------*/

/**
 * @brief 限制参数在有效范围内
 */
#define TUNER_CONSTRAIN_KP(kp) \
    ((kp) < TUNER_MIN_KP ? TUNER_MIN_KP : ((kp) > TUNER_MAX_KP ? TUNER_MAX_KP : (kp)))

#define TUNER_CONSTRAIN_KI(ki) \
    ((ki) < TUNER_MIN_KI ? TUNER_MIN_KI : ((ki) > TUNER_MAX_KI ? TUNER_MAX_KI : (ki)))

#define TUNER_CONSTRAIN_KD(kd) \
    ((kd) < TUNER_MIN_KD ? TUNER_MIN_KD : ((kd) > TUNER_MAX_KD ? TUNER_MAX_KD : (kd)))

/* Exported functions prototypes ---------------------------------------------*/

/** @defgroup Tuner_Initialization_Functions 初始化函数
 * @{
 */
HAL_StatusTypeDef PID_Tuner_Init(PID_Tuner_t *tuner, Balance_System_t *system);
void PID_Tuner_Reset(PID_Tuner_t *tuner);
/**
 * @}
 */

/** @defgroup Tuner_Command_Functions 命令处理函数
 * @{
 */
void PID_Tuner_ProcessCommand(PID_Tuner_t *tuner, const char *command);
void PID_Tuner_ParseSerialInput(PID_Tuner_t *tuner, char received_char);
void PID_Tuner_PrintHelp(void);
void PID_Tuner_PrintCurrentParams(PID_Tuner_t *tuner);
/**
 * @}
 */

/** @defgroup Tuner_Manual_Functions 手动调节函数
 * @{
 */
void PID_Tuner_SetKp(PID_Tuner_t *tuner, float kp);
void PID_Tuner_SetKi(PID_Tuner_t *tuner, float ki);
void PID_Tuner_SetKd(PID_Tuner_t *tuner, float kd);
void PID_Tuner_SetAllParams(PID_Tuner_t *tuner, float kp, float ki, float kd);
/**
 * @}
 */

/** @defgroup Tuner_Auto_Functions 自动调节函数
 * @{
 */
HAL_StatusTypeDef PID_Tuner_StartAutoTuning(PID_Tuner_t *tuner);
void PID_Tuner_StopAutoTuning(PID_Tuner_t *tuner);
void PID_Tuner_UpdateAutoTuning(PID_Tuner_t *tuner);
float PID_Tuner_EvaluatePerformance(PID_Tuner_t *tuner);
/**
 * @}
 */

/** @defgroup Tuner_Storage_Functions 存储函数
 * @{
 */
void PID_Tuner_SaveParams(PID_Tuner_t *tuner);
void PID_Tuner_LoadParams(PID_Tuner_t *tuner);
void PID_Tuner_BackupParams(PID_Tuner_t *tuner);
void PID_Tuner_RestoreParams(PID_Tuner_t *tuner);
/**
 * @}
 */

/** @defgroup Tuner_Utility_Functions 工具函数
 * @{
 */
void PID_Tuner_Update(PID_Tuner_t *tuner);
uint8_t PID_Tuner_IsAutoTuning(const PID_Tuner_t *tuner);
const char* PID_Tuner_GetModeString(PID_Tuner_Mode_t mode);
/**
 * @}
 */

/* Global tuner instance -----------------------------------------------------*/
extern PID_Tuner_t g_pid_tuner;

#ifdef __cplusplus
}
#endif

#endif /* __PID_TUNER_H */

/**
 * @}
 */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
