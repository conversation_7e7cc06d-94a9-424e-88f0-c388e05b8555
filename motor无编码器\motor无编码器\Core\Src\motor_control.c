/**
 ******************************************************************************
 * @file    motor_control.c
 * @brief   电机控制器实现文件 - TB6612FNG双电机驱动控制
 * <AUTHOR> (工程师)
 * @version v1.0
 * @date    2025-01-15
 ******************************************************************************
 */

/* Includes ------------------------------------------------------------------*/
#include "motor_control.h"
#include <stdio.h>
#include <string.h>
#include <math.h>

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/
static void Motor_SetDirectionPins(Motor_Direction_t direction);
static HAL_StatusTypeDef Motor_SetSingleMotorPWM(Motor_Controller_t *motor, Motor_ID_t motor_id, uint16_t pwm_value);
static void Motor_UpdateSingleMotorStatus(Motor_Controller_t *motor, Motor_ID_t motor_id);
static uint8_t Motor_ValidateSpeed(float speed);

/* Exported functions --------------------------------------------------------*/

/**
 * @brief  初始化电机控制器
 * @param  motor: 电机控制器指针
 * @param  htim: 定时器句柄指针
 * @retval HAL状态
 */
HAL_StatusTypeDef Motor_Init(Motor_Controller_t *motor, TIM_HandleTypeDef *htim)
{
    if (motor == NULL || htim == NULL) {
        return HAL_ERROR;
    }
    
    // 清零结构体
    memset(motor, 0, sizeof(Motor_Controller_t));
    
    // 设置定时器句柄
    motor->htim = htim;
    motor->pwm_channel_left = MOTOR_PWM_CHANNEL_LEFT;
    motor->pwm_channel_right = MOTOR_PWM_CHANNEL_RIGHT;
    
    // 设置默认配置
    if (Motor_SetDefaultConfig(motor) != HAL_OK) {
        motor->state = MOTOR_STATE_FAULT;
        return HAL_ERROR;
    }
    
    // 初始化GPIO方向控制引脚
    Motor_SetDirectionPins(MOTOR_DIR_STOP);
    
    // 启动PWM输出
    if (Motor_StartPWM(motor) != HAL_OK) {
        motor->state = MOTOR_STATE_FAULT;
        return HAL_ERROR;
    }
    
    // 设置初始状态
    motor->state = MOTOR_STATE_ENABLED;
    motor->last_update_time = HAL_GetTick();
    
    printf("Motor Controller: Initialized successfully\r\n");
    return HAL_OK;
}

/**
 * @brief  设置电机配置
 * @param  motor: 电机控制器指针
 * @param  config: 配置参数指针
 * @retval HAL状态
 */
HAL_StatusTypeDef Motor_SetConfig(Motor_Controller_t *motor, const Motor_Config_t *config)
{
    if (motor == NULL || config == NULL) {
        return HAL_ERROR;
    }
    
    // 验证配置参数
    if (config->max_pwm_value == 0 || config->max_pwm_value > 1000 ||
        config->min_pwm_value >= config->max_pwm_value ||
        config->speed_limit <= 0.0f || config->speed_limit > 1.0f) {
        return HAL_ERROR;
    }
    
    // 复制配置
    memcpy(&motor->config, config, sizeof(Motor_Config_t));
    
    return HAL_OK;
}

/**
 * @brief  设置默认配置
 * @param  motor: 电机控制器指针
 * @retval HAL状态
 */
HAL_StatusTypeDef Motor_SetDefaultConfig(Motor_Controller_t *motor)
{
    if (motor == NULL) {
        return HAL_ERROR;
    }
    
    motor->config.max_pwm_value = MOTOR_DEFAULT_MAX_PWM;
    motor->config.min_pwm_value = MOTOR_DEFAULT_MIN_PWM;
    motor->config.deadzone_pwm = MOTOR_DEFAULT_DEADZONE_PWM;
    motor->config.speed_limit = MOTOR_DEFAULT_SPEED_LIMIT;
    motor->config.enable_soft_start = 1;
    motor->config.soft_start_time = MOTOR_DEFAULT_SOFT_START_TIME;
    motor->config.enable_brake = 1;
    motor->config.brake_time = MOTOR_DEFAULT_BRAKE_TIME;
    
    return HAL_OK;
}

/**
 * @brief  重置电机控制器
 * @param  motor: 电机控制器指针
 * @retval None
 */
void Motor_Reset(Motor_Controller_t *motor)
{
    if (motor == NULL) return;
    
    // 停止电机
    Motor_Stop(motor);
    
    // 重置状态
    memset(&motor->left_motor, 0, sizeof(Motor_Status_t));
    memset(&motor->right_motor, 0, sizeof(Motor_Status_t));
    
    // 重置时间戳
    motor->last_update_time = HAL_GetTick();
    motor->total_run_time = 0;
    motor->emergency_count = 0;
    
    motor->state = MOTOR_STATE_ENABLED;
    
    printf("Motor Controller: Reset\r\n");
}

/**
 * @brief  设置电机速度 (双电机同步)
 * @param  motor: 电机控制器指针
 * @param  speed: 速度值 (-1.0 ~ 1.0, 负值为后退)
 * @retval HAL状态
 */
HAL_StatusTypeDef Motor_SetSpeed(Motor_Controller_t *motor, float speed)
{
    if (motor == NULL || motor->state != MOTOR_STATE_ENABLED) {
        return HAL_ERROR;
    }
    
    // 验证速度值
    if (!Motor_ValidateSpeed(speed)) {
        return HAL_ERROR;
    }
    
    // 速度限制
    speed = MOTOR_CONSTRAIN_SPEED(speed);
    speed *= motor->config.speed_limit;
    
    // 确定方向
    Motor_Direction_t direction;
    if (fabsf(speed) < 0.01f) {
        direction = MOTOR_DIR_STOP;
    } else if (speed > 0.0f) {
        direction = MOTOR_DIR_FORWARD;
    } else {
        direction = MOTOR_DIR_BACKWARD;
    }
    
    // 设置方向
    Motor_SetDirectionPins(direction);
    
    // 计算PWM值
    uint16_t pwm_value = MOTOR_SPEED_TO_PWM(speed, motor->config.max_pwm_value);
    
    // 死区处理
    if (pwm_value > 0 && pwm_value < motor->config.deadzone_pwm) {
        pwm_value = motor->config.deadzone_pwm;
    }
    
    // 设置PWM
    HAL_StatusTypeDef status = HAL_OK;
    status |= Motor_SetSingleMotorPWM(motor, MOTOR_LEFT, pwm_value);
    status |= Motor_SetSingleMotorPWM(motor, MOTOR_RIGHT, pwm_value);
    
    // 更新状态
    motor->left_motor.speed_command = speed;
    motor->right_motor.speed_command = speed;
    motor->left_motor.direction = direction;
    motor->right_motor.direction = direction;
    
    return status;
}

/**
 * @brief  设置差分电机速度 (左右电机独立控制)
 * @param  motor: 电机控制器指针
 * @param  left_speed: 左电机速度 (-1.0 ~ 1.0)
 * @param  right_speed: 右电机速度 (-1.0 ~ 1.0)
 * @retval HAL状态
 */
HAL_StatusTypeDef Motor_SetSpeedDifferential(Motor_Controller_t *motor, float left_speed, float right_speed)
{
    if (motor == NULL || motor->state != MOTOR_STATE_ENABLED) {
        return HAL_ERROR;
    }
    
    // 验证速度值
    if (!Motor_ValidateSpeed(left_speed) || !Motor_ValidateSpeed(right_speed)) {
        return HAL_ERROR;
    }
    
    // 速度限制
    left_speed = MOTOR_CONSTRAIN_SPEED(left_speed) * motor->config.speed_limit;
    right_speed = MOTOR_CONSTRAIN_SPEED(right_speed) * motor->config.speed_limit;
    
    // 确定整体方向 (基于平均速度)
    float avg_speed = (left_speed + right_speed) / 2.0f;
    Motor_Direction_t direction;
    if (fabsf(avg_speed) < 0.01f) {
        direction = MOTOR_DIR_STOP;
    } else if (avg_speed > 0.0f) {
        direction = MOTOR_DIR_FORWARD;
    } else {
        direction = MOTOR_DIR_BACKWARD;
    }
    
    // 设置方向 (使用整体方向)
    Motor_SetDirectionPins(direction);
    
    // 计算PWM值
    uint16_t left_pwm = MOTOR_SPEED_TO_PWM(left_speed, motor->config.max_pwm_value);
    uint16_t right_pwm = MOTOR_SPEED_TO_PWM(right_speed, motor->config.max_pwm_value);
    
    // 死区处理
    if (left_pwm > 0 && left_pwm < motor->config.deadzone_pwm) {
        left_pwm = motor->config.deadzone_pwm;
    }
    if (right_pwm > 0 && right_pwm < motor->config.deadzone_pwm) {
        right_pwm = motor->config.deadzone_pwm;
    }
    
    // 设置PWM
    HAL_StatusTypeDef status = HAL_OK;
    status |= Motor_SetSingleMotorPWM(motor, MOTOR_LEFT, left_pwm);
    status |= Motor_SetSingleMotorPWM(motor, MOTOR_RIGHT, right_pwm);
    
    // 更新状态
    motor->left_motor.speed_command = left_speed;
    motor->right_motor.speed_command = right_speed;
    motor->left_motor.direction = (left_speed >= 0) ? MOTOR_DIR_FORWARD : MOTOR_DIR_BACKWARD;
    motor->right_motor.direction = (right_speed >= 0) ? MOTOR_DIR_FORWARD : MOTOR_DIR_BACKWARD;
    
    return status;
}

/**
 * @brief  设置电机方向
 * @param  motor: 电机控制器指针
 * @param  direction: 电机方向
 * @retval HAL状态
 */
HAL_StatusTypeDef Motor_SetDirection(Motor_Controller_t *motor, Motor_Direction_t direction)
{
    if (motor == NULL || motor->state != MOTOR_STATE_ENABLED) {
        return HAL_ERROR;
    }
    
    Motor_SetDirectionPins(direction);
    
    motor->left_motor.direction = direction;
    motor->right_motor.direction = direction;
    
    return HAL_OK;
}

/**
 * @brief  设置单个电机PWM值
 * @param  motor: 电机控制器指针
 * @param  motor_id: 电机ID
 * @param  pwm_value: PWM值
 * @retval HAL状态
 */
HAL_StatusTypeDef Motor_SetPWM(Motor_Controller_t *motor, Motor_ID_t motor_id, uint16_t pwm_value)
{
    if (motor == NULL || motor->state != MOTOR_STATE_ENABLED) {
        return HAL_ERROR;
    }
    
    return Motor_SetSingleMotorPWM(motor, motor_id, pwm_value);
}

/**
 * @brief  停止电机
 * @param  motor: 电机控制器指针
 * @retval None
 */
void Motor_Stop(Motor_Controller_t *motor)
{
    if (motor == NULL) return;
    
    // 设置PWM为0
    Motor_SetSingleMotorPWM(motor, MOTOR_LEFT, 0);
    Motor_SetSingleMotorPWM(motor, MOTOR_RIGHT, 0);
    
    // 设置方向为停止
    Motor_SetDirectionPins(MOTOR_DIR_STOP);
    
    // 更新状态
    motor->left_motor.speed_command = 0.0f;
    motor->right_motor.speed_command = 0.0f;
    motor->left_motor.direction = MOTOR_DIR_STOP;
    motor->right_motor.direction = MOTOR_DIR_STOP;
    
    printf("Motor Controller: Stopped\r\n");
}

/**
 * @brief  紧急停止电机
 * @param  motor: 电机控制器指针
 * @retval None
 */
void Motor_EmergencyStop(Motor_Controller_t *motor)
{
    if (motor == NULL) return;
    
    motor->state = MOTOR_STATE_EMERGENCY;
    motor->emergency_count++;
    
    // 立即停止所有输出
    Motor_Stop(motor);
    
    printf("Motor Controller: EMERGENCY STOP! Count: %u\r\n", (unsigned int)motor->emergency_count);
}

/**
 * @brief  电机刹车
 * @param  motor: 电机控制器指针
 * @retval None
 */
void Motor_Brake(Motor_Controller_t *motor)
{
    if (motor == NULL || !motor->config.enable_brake) return;

    // 短暂反向制动
    Motor_Direction_t current_dir = motor->left_motor.direction;

    if (current_dir == MOTOR_DIR_FORWARD) {
        Motor_SetDirectionPins(MOTOR_DIR_BACKWARD);
    } else if (current_dir == MOTOR_DIR_BACKWARD) {
        Motor_SetDirectionPins(MOTOR_DIR_FORWARD);
    }

    // 短暂制动后停止
    HAL_Delay(motor->config.brake_time);
    Motor_Stop(motor);
}

/* Enable/Disable Functions --------------------------------------------------*/

/**
 * @brief  启用电机控制器
 * @param  motor: 电机控制器指针
 * @retval None
 */
void Motor_Enable(Motor_Controller_t *motor)
{
    if (motor != NULL && motor->state != MOTOR_STATE_FAULT) {
        motor->state = MOTOR_STATE_ENABLED;
        printf("Motor Controller: Enabled\r\n");
    }
}

/**
 * @brief  禁用电机控制器
 * @param  motor: 电机控制器指针
 * @retval None
 */
void Motor_Disable(Motor_Controller_t *motor)
{
    if (motor != NULL) {
        Motor_Stop(motor);
        motor->state = MOTOR_STATE_DISABLED;
        printf("Motor Controller: Disabled\r\n");
    }
}

/**
 * @brief  启动PWM输出
 * @param  motor: 电机控制器指针
 * @retval HAL状态
 */
HAL_StatusTypeDef Motor_StartPWM(Motor_Controller_t *motor)
{
    if (motor == NULL || motor->htim == NULL) {
        return HAL_ERROR;
    }

    HAL_StatusTypeDef status = HAL_OK;

    // 启动左电机PWM
    status |= HAL_TIM_PWM_Start(motor->htim, motor->pwm_channel_left);

    // 启动右电机PWM
    status |= HAL_TIM_PWM_Start(motor->htim, motor->pwm_channel_right);

    if (status == HAL_OK) {
        printf("Motor Controller: PWM started\r\n");
    } else {
        printf("Motor Controller: PWM start failed\r\n");
    }

    return status;
}

/**
 * @brief  停止PWM输出
 * @param  motor: 电机控制器指针
 * @retval HAL状态
 */
HAL_StatusTypeDef Motor_StopPWM(Motor_Controller_t *motor)
{
    if (motor == NULL || motor->htim == NULL) {
        return HAL_ERROR;
    }

    HAL_StatusTypeDef status = HAL_OK;

    // 停止左电机PWM
    status |= HAL_TIM_PWM_Stop(motor->htim, motor->pwm_channel_left);

    // 停止右电机PWM
    status |= HAL_TIM_PWM_Stop(motor->htim, motor->pwm_channel_right);

    printf("Motor Controller: PWM stopped\r\n");
    return status;
}

/* Status Query Functions ----------------------------------------------------*/

/**
 * @brief  获取电机控制器状态
 * @param  motor: 电机控制器指针
 * @retval 电机状态
 */
Motor_State_t Motor_GetState(const Motor_Controller_t *motor)
{
    if (motor == NULL) {
        return MOTOR_STATE_FAULT;
    }
    return motor->state;
}

/**
 * @brief  获取单个电机状态
 * @param  motor: 电机控制器指针
 * @param  motor_id: 电机ID
 * @retval 电机状态指针
 */
Motor_Status_t* Motor_GetMotorStatus(Motor_Controller_t *motor, Motor_ID_t motor_id)
{
    if (motor == NULL) {
        return NULL;
    }

    switch (motor_id) {
        case MOTOR_LEFT:
            return &motor->left_motor;
        case MOTOR_RIGHT:
            return &motor->right_motor;
        default:
            return NULL;
    }
}

/**
 * @brief  检查电机是否启用
 * @param  motor: 电机控制器指针
 * @retval 1: 启用, 0: 禁用
 */
uint8_t Motor_IsEnabled(const Motor_Controller_t *motor)
{
    if (motor == NULL) {
        return 0;
    }
    return (motor->state == MOTOR_STATE_ENABLED);
}

/**
 * @brief  获取PWM值
 * @param  motor: 电机控制器指针
 * @param  motor_id: 电机ID
 * @retval PWM值
 */
uint16_t Motor_GetPWMValue(const Motor_Controller_t *motor, Motor_ID_t motor_id)
{
    if (motor == NULL) {
        return 0;
    }

    switch (motor_id) {
        case MOTOR_LEFT:
            return motor->left_motor.pwm_value;
        case MOTOR_RIGHT:
            return motor->right_motor.pwm_value;
        default:
            return 0;
    }
}

/**
 * @brief  获取电机速度
 * @param  motor: 电机控制器指针
 * @param  motor_id: 电机ID
 * @retval 速度值
 */
float Motor_GetSpeed(const Motor_Controller_t *motor, Motor_ID_t motor_id)
{
    if (motor == NULL) {
        return 0.0f;
    }

    switch (motor_id) {
        case MOTOR_LEFT:
            return motor->left_motor.speed_command;
        case MOTOR_RIGHT:
            return motor->right_motor.speed_command;
        default:
            return 0.0f;
    }
}

/**
 * @brief  获取电机方向
 * @param  motor: 电机控制器指针
 * @param  motor_id: 电机ID
 * @retval 电机方向
 */
Motor_Direction_t Motor_GetDirection(const Motor_Controller_t *motor, Motor_ID_t motor_id)
{
    if (motor == NULL) {
        return MOTOR_DIR_STOP;
    }

    switch (motor_id) {
        case MOTOR_LEFT:
            return motor->left_motor.direction;
        case MOTOR_RIGHT:
            return motor->right_motor.direction;
        default:
            return MOTOR_DIR_STOP;
    }
}

/* Utility Functions ---------------------------------------------------------*/

/**
 * @brief  打印电机状态
 * @param  motor: 电机控制器指针
 * @retval None
 */
void Motor_PrintStatus(const Motor_Controller_t *motor)
{
    if (motor == NULL) {
        printf("Motor: NULL pointer\r\n");
        return;
    }

    printf("=== Motor Status ===\r\n");
    printf("State: %s\r\n", Motor_GetStateString(motor->state));
    printf("Left Motor: Speed=%.2f, PWM=%d, Dir=%s\r\n",
           motor->left_motor.speed_command, motor->left_motor.pwm_value,
           Motor_GetDirectionString(motor->left_motor.direction));
    printf("Right Motor: Speed=%.2f, PWM=%d, Dir=%s\r\n",
           motor->right_motor.speed_command, motor->right_motor.pwm_value,
           Motor_GetDirectionString(motor->right_motor.direction));
    printf("Run Time: %ums, Emergency Count: %u\r\n",
           (unsigned int)motor->total_run_time, (unsigned int)motor->emergency_count);
    printf("====================\r\n");
}

/**
 * @brief  获取状态字符串
 * @param  state: 状态枚举
 * @retval 状态字符串
 */
const char* Motor_GetStateString(Motor_State_t state)
{
    switch (state) {
        case MOTOR_STATE_DISABLED:  return "DISABLED";
        case MOTOR_STATE_ENABLED:   return "ENABLED";
        case MOTOR_STATE_FAULT:     return "FAULT";
        case MOTOR_STATE_EMERGENCY: return "EMERGENCY";
        default:                    return "UNKNOWN";
    }
}

/**
 * @brief  获取方向字符串
 * @param  direction: 方向枚举
 * @retval 方向字符串
 */
const char* Motor_GetDirectionString(Motor_Direction_t direction)
{
    switch (direction) {
        case MOTOR_DIR_STOP:     return "STOP";
        case MOTOR_DIR_FORWARD:  return "FORWARD";
        case MOTOR_DIR_BACKWARD: return "BACKWARD";
        default:                 return "UNKNOWN";
    }
}

/**
 * @brief  自检测试
 * @param  motor: 电机控制器指针
 * @retval HAL状态
 */
HAL_StatusTypeDef Motor_SelfTest(Motor_Controller_t *motor)
{
    if (motor == NULL) {
        return HAL_ERROR;
    }

    printf("Motor Controller: Starting self-test...\r\n");

    // 测试PWM输出
    Motor_SetSpeed(motor, 0.1f);
    HAL_Delay(100);
    Motor_SetSpeed(motor, -0.1f);
    HAL_Delay(100);
    Motor_Stop(motor);

    printf("Motor Controller: Self-test completed\r\n");
    return HAL_OK;
}

/* Private Functions ---------------------------------------------------------*/

/**
 * @brief  设置方向控制引脚
 * @param  direction: 电机方向
 * @retval None
 */
static void Motor_SetDirectionPins(Motor_Direction_t direction)
{
    switch (direction) {
        case MOTOR_DIR_FORWARD:
            // 前进: AIN1=1, AIN2=0, BIN1=1, BIN2=0
            HAL_GPIO_WritePin(AIN1_GPIO_Port, AIN1_Pin, GPIO_PIN_SET);
            HAL_GPIO_WritePin(AIN2_GPIO_Port, AIN2_Pin, GPIO_PIN_RESET);
            HAL_GPIO_WritePin(BIN1_GPIO_Port, BIN1_Pin, GPIO_PIN_SET);
            HAL_GPIO_WritePin(BIN2_GPIO_Port, BIN2_Pin, GPIO_PIN_RESET);
            break;

        case MOTOR_DIR_BACKWARD:
            // 后退: AIN1=0, AIN2=1, BIN1=0, BIN2=1
            HAL_GPIO_WritePin(AIN1_GPIO_Port, AIN1_Pin, GPIO_PIN_RESET);
            HAL_GPIO_WritePin(AIN2_GPIO_Port, AIN2_Pin, GPIO_PIN_SET);
            HAL_GPIO_WritePin(BIN1_GPIO_Port, BIN1_Pin, GPIO_PIN_RESET);
            HAL_GPIO_WritePin(BIN2_GPIO_Port, BIN2_Pin, GPIO_PIN_SET);
            break;

        case MOTOR_DIR_STOP:
        default:
            // 停止: 所有引脚为0 (短路制动)
            HAL_GPIO_WritePin(AIN1_GPIO_Port, AIN1_Pin, GPIO_PIN_RESET);
            HAL_GPIO_WritePin(AIN2_GPIO_Port, AIN2_Pin, GPIO_PIN_RESET);
            HAL_GPIO_WritePin(BIN1_GPIO_Port, BIN1_Pin, GPIO_PIN_RESET);
            HAL_GPIO_WritePin(BIN2_GPIO_Port, BIN2_Pin, GPIO_PIN_RESET);
            break;
    }
}

/**
 * @brief  设置单个电机PWM值
 * @param  motor: 电机控制器指针
 * @param  motor_id: 电机ID
 * @param  pwm_value: PWM值
 * @retval HAL状态
 */
static HAL_StatusTypeDef Motor_SetSingleMotorPWM(Motor_Controller_t *motor, Motor_ID_t motor_id, uint16_t pwm_value)
{
    if (motor == NULL || motor->htim == NULL) {
        return HAL_ERROR;
    }

    // PWM值限制
    pwm_value = MOTOR_CONSTRAIN_PWM(pwm_value, motor->config.max_pwm_value);

    HAL_StatusTypeDef status = HAL_OK;

    switch (motor_id) {
        case MOTOR_LEFT:
            __HAL_TIM_SET_COMPARE(motor->htim, motor->pwm_channel_left, pwm_value);
            motor->left_motor.pwm_value = pwm_value;
            break;

        case MOTOR_RIGHT:
            __HAL_TIM_SET_COMPARE(motor->htim, motor->pwm_channel_right, pwm_value);
            motor->right_motor.pwm_value = pwm_value;
            break;

        case MOTOR_BOTH:
            __HAL_TIM_SET_COMPARE(motor->htim, motor->pwm_channel_left, pwm_value);
            __HAL_TIM_SET_COMPARE(motor->htim, motor->pwm_channel_right, pwm_value);
            motor->left_motor.pwm_value = pwm_value;
            motor->right_motor.pwm_value = pwm_value;
            break;

        default:
            status = HAL_ERROR;
            break;
    }

    return status;
}

/**
 * @brief  更新单个电机状态
 * @param  motor: 电机控制器指针
 * @param  motor_id: 电机ID
 * @retval None
 */
static void Motor_UpdateSingleMotorStatus(Motor_Controller_t *motor, Motor_ID_t motor_id)
{
    if (motor == NULL) return;

    uint32_t current_time = HAL_GetTick();
    Motor_Status_t *status = NULL;

    switch (motor_id) {
        case MOTOR_LEFT:
            status = &motor->left_motor;
            break;
        case MOTOR_RIGHT:
            status = &motor->right_motor;
            break;
        default:
            return;
    }

    // 更新运行时间
    if (status->pwm_value > 0) {
        status->run_time = current_time - motor->last_update_time;
        status->is_enabled = 1;
    } else {
        status->is_enabled = 0;
    }

    // 计算实际速度 (基于PWM值)
    status->actual_speed = (float)status->pwm_value / motor->config.max_pwm_value;
    if (status->direction == MOTOR_DIR_BACKWARD) {
        status->actual_speed = -status->actual_speed;
    }
}

/**
 * @brief  验证速度值有效性
 * @param  speed: 速度值
 * @retval 1: 有效, 0: 无效
 */
static uint8_t Motor_ValidateSpeed(float speed)
{
    // 检查是否为有效数值
    if (isnan(speed) || isinf(speed)) {
        return 0;
    }

    // 检查范围
    if (speed < -1.0f || speed > 1.0f) {
        return 0;
    }

    return 1;
}

/**
 * @brief  软启动功能
 * @param  motor: 电机控制器指针
 * @param  target_speed: 目标速度
 * @retval HAL状态
 */
HAL_StatusTypeDef Motor_SoftStart(Motor_Controller_t *motor, float target_speed)
{
    if (motor == NULL || !motor->config.enable_soft_start) {
        return Motor_SetSpeed(motor, target_speed);
    }

    const int steps = 20;
    float speed_step = target_speed / steps;
    uint32_t delay_step = motor->config.soft_start_time / steps;

    for (int i = 1; i <= steps; i++) {
        float current_speed = speed_step * i;
        Motor_SetSpeed(motor, current_speed);
        HAL_Delay(delay_step);
    }

    return HAL_OK;
}

/**
 * @brief  软停止功能
 * @param  motor: 电机控制器指针
 * @retval HAL状态
 */
HAL_StatusTypeDef Motor_SoftStop(Motor_Controller_t *motor)
{
    if (motor == NULL) {
        return HAL_ERROR;
    }

    float current_speed = (motor->left_motor.speed_command + motor->right_motor.speed_command) / 2.0f;

    const int steps = 10;
    float speed_step = current_speed / steps;
    uint32_t delay_step = motor->config.brake_time / steps;

    for (int i = steps - 1; i >= 0; i--) {
        float new_speed = speed_step * i;
        Motor_SetSpeed(motor, new_speed);
        HAL_Delay(delay_step);
    }

    Motor_Stop(motor);
    return HAL_OK;
}

/**
 * @brief  更新电机状态
 * @param  motor: 电机控制器指针
 * @retval None
 */
void Motor_UpdateStatus(Motor_Controller_t *motor)
{
    if (motor == NULL) return;

    uint32_t current_time = HAL_GetTick();

    // 更新总运行时间
    motor->total_run_time = current_time - motor->last_update_time;

    // 更新单个电机状态
    Motor_UpdateSingleMotorStatus(motor, MOTOR_LEFT);
    Motor_UpdateSingleMotorStatus(motor, MOTOR_RIGHT);

    motor->last_update_time = current_time;
}

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
