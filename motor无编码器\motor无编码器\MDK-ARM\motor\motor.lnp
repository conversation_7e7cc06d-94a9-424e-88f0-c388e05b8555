--cpu=Cortex-M4.fp.sp
"motor\startup_stm32f407xx.o"
"motor\main.o"
"motor\gpio.o"
"motor\dma.o"
"motor\i2c.o"
"motor\tim.o"
"motor\usart.o"
"motor\stm32f4xx_it.o"
"motor\stm32f4xx_hal_msp.o"
"motor\mpu6050.o"
"motor\attitude.o"
"motor\stm32f4xx_hal_i2c.o"
"motor\stm32f4xx_hal_i2c_ex.o"
"motor\stm32f4xx_hal_rcc.o"
"motor\stm32f4xx_hal_rcc_ex.o"
"motor\stm32f4xx_hal_flash.o"
"motor\stm32f4xx_hal_flash_ex.o"
"motor\stm32f4xx_hal_flash_ramfunc.o"
"motor\stm32f4xx_hal_gpio.o"
"motor\stm32f4xx_hal_dma_ex.o"
"motor\stm32f4xx_hal_dma.o"
"motor\stm32f4xx_hal_pwr.o"
"motor\stm32f4xx_hal_pwr_ex.o"
"motor\stm32f4xx_hal_cortex.o"
"motor\stm32f4xx_hal.o"
"motor\stm32f4xx_hal_exti.o"
"motor\stm32f4xx_hal_tim.o"
"motor\stm32f4xx_hal_tim_ex.o"
"motor\stm32f4xx_hal_uart.o"
"motor\system_stm32f4xx.o"
--strict --scatter "motor\motor.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "motor.map" -o motor\motor.axf