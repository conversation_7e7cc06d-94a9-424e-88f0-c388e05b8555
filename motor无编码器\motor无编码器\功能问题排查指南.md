# 🔧 STM32F4 PID平衡车控制系统 - 功能问题排查指南

## 📋 问题排查概述

**检查时间**: 2025-01-15  
**问题描述**: 程序编译成功但无法实现平衡功能  
**排查状态**: 🔍 **正在进行系统性排查**  

---

## 🎯 新增系统诊断功能

我已经为您添加了完整的系统诊断工具，现在程序启动时会自动检查所有关键模块：

### ✅ 诊断功能包括
1. **系统时钟检查** - 验证84MHz时钟配置
2. **GPIO配置检查** - 验证电机方向控制引脚
3. **UART通信检查** - 验证串口通信正常
4. **I2C总线检查** - 扫描I2C设备
5. **MPU6050传感器检查** - 验证传感器连接和数据读取
6. **PWM生成检查** - 验证电机PWM输出
7. **电机控制检查** - 验证方向控制功能
8. **内存系统检查** - 验证RAM读写正常

### 📊 诊断输出示例
```
=== Running System Diagnostics ===
Testing system clock...
  SYSCLK: 84000000 Hz
  ✅ Clock frequencies OK

Testing GPIO configuration...
  ✅ Motor direction pins OK

Testing UART communication...
  ✅ UART communication OK

Testing I2C bus...
  Device found at address: 0x68
  ✅ I2C bus OK, 1 device(s) found

Testing MPU6050 sensor...
  ✅ MPU6050 connection OK
  ✅ MPU6050 data reading OK

=== DIAGNOSTIC REPORT ===
✅ ALL TESTS PASSED - System ready for operation!
```

---

## 🔍 常见问题及解决方案

### 问题1: MPU6050传感器连接失败
**症状**: 
```
❌ MPU6050 connection failed
No I2C devices found
```

**解决方案**:
1. **检查硬件连接**:
   ```
   MPU6050    →    STM32F407
   VCC        →    3.3V (不是5V!)
   GND        →    GND
   SCL        →    PB6
   SDA        →    PB7
   ```

2. **添加上拉电阻** (重要!):
   - SCL和SDA线各需要4.7kΩ上拉电阻到3.3V
   - 没有上拉电阻I2C通信会失败

3. **检查供电**:
   - 确保MPU6050供电为3.3V
   - 检查GND连接良好

### 问题2: 电机不转动
**症状**: 
```
✅ PWM generation OK
但电机没有实际转动
```

**解决方案**:
1. **检查电机驱动连接**:
   ```
   TB6612FNG  →    STM32F407
   PWMA       →    PE9  (TIM1_CH1)
   PWMB       →    PE11 (TIM1_CH2)
   AIN1       →    PE12 (方向控制1)
   AIN2       →    PE14 (方向控制2)
   BIN1       →    PE8  (方向控制3)
   BIN2       →    PE10 (方向控制4)
   ```

2. **检查电机供电**:
   - 电机需要6-12V独立供电
   - TB6612的VCC连接到3.3V (逻辑电源)
   - VM连接到电机电源 (6-12V)

3. **检查STBY引脚**:
   - TB6612的STBY引脚必须连接到3.3V
   - 否则驱动器处于待机状态

### 问题3: 系统启动后立即停止
**症状**: 
```
ERROR: Balance system initialization failed!
```

**解决方案**:
1. **查看详细错误信息** - 诊断工具会显示具体失败的模块
2. **检查传感器** - 通常是MPU6050连接问题
3. **检查电机驱动** - 确保PWM和方向控制正常

### 问题4: 平衡车不平衡
**症状**: 
- 系统启动正常
- 但平衡车无法保持直立

**解决方案**:
1. **检查机械结构**:
   - 重心是否合适
   - 轮子是否平行
   - 机械连接是否牢固

2. **调节PID参数**:
   ```bash
   # 在串口中输入
   kp 25.0    # 增大比例系数
   kd 1.5     # 增大微分系数
   auto       # 启动自动调节
   ```

3. **检查传感器安装**:
   - MPU6050必须水平安装
   - 传感器方向要正确

---

## 🚀 立即排查步骤

### Step 1: 编译并下载新程序 (5分钟)
```
1. 在Keil中编译项目 (F7)
2. 下载程序到STM32 (F8)
3. 打开串口助手 (115200波特率)
```

### Step 2: 查看诊断报告 (2分钟)
```
1. 上电后观察串口输出
2. 查看系统诊断报告
3. 记录所有失败的测试项目
```

### Step 3: 根据诊断结果修复问题 (10-30分钟)
```
1. 如果I2C失败 → 检查MPU6050连接和上拉电阻
2. 如果PWM失败 → 检查定时器配置
3. 如果电机失败 → 检查TB6612连接和供电
```

### Step 4: 重新测试 (5分钟)
```
1. 修复问题后重新上电
2. 确认所有诊断测试通过
3. 测试平衡功能
```

---

## 🔧 硬件检查清单

### ✅ STM32F407开发板
- [ ] 供电正常 (5V或USB供电)
- [ ] ST-Link连接正常
- [ ] 程序下载成功

### ✅ MPU6050传感器模块
- [ ] VCC → 3.3V (不是5V!)
- [ ] GND → GND
- [ ] SCL → PB6
- [ ] SDA → PB7
- [ ] 添加4.7kΩ上拉电阻 (SCL和SDA到3.3V)

### ✅ TB6612FNG电机驱动
- [ ] VCC → 3.3V (逻辑电源)
- [ ] VM → 6-12V (电机电源)
- [ ] GND → GND
- [ ] STBY → 3.3V (使能驱动器)
- [ ] PWMA → PE9
- [ ] PWMB → PE11
- [ ] AIN1 → PE12, AIN2 → PE14
- [ ] BIN1 → PE8, BIN2 → PE10

### ✅ 直流减速电机
- [ ] 左电机连接到AO1, AO2
- [ ] 右电机连接到BO1, BO2
- [ ] 电机供电6-12V

### ✅ 串口调试
- [ ] TX → PA9, RX → PA10
- [ ] 波特率115200
- [ ] 能看到启动信息

---

## 📊 预期的正常输出

### 启动序列
```
=== PID Balance Car System Starting ===
=== Running System Diagnostics ===
[PASS] System Clock: 84MHz HSE PLL
[PASS] GPIO Configuration: All pins configured
[PASS] UART Communication: 115200 baud OK
[PASS] I2C Bus: I2C1 400kHz OK
[PASS] MPU6050 Sensor: Device ID 0x68 OK
[PASS] PWM Generation: TIM1 CH1/CH2 OK
[PASS] Motor Control: Direction pins OK
[PASS] Memory System: RAM/Flash OK

✅ ALL TESTS PASSED - System ready for operation!

=== Balance System Initialization ===
Initializing MPU6050 sensor...
SUCCESS: MPU6050 device detected (ID: 0x68)
Initializing attitude calculation...
Initializing balance controller...
Initializing motor controller...
Motor Controller: PWM started
Motor Controller: Initialized successfully
SUCCESS: Balance system initialized!
```

### 运行时数据
```
T:12345,S:3,A:1.23,E:-0.45,O:123.4,B:1,L:456,R:456
```

---

## 🆘 如果问题仍然存在

### 1. 提供诊断报告
请将完整的串口输出发送给我，特别是：
- 系统诊断报告
- 任何错误信息
- 失败的测试项目

### 2. 检查硬件照片
如果可能，请检查：
- 接线是否正确
- 上拉电阻是否添加
- 供电是否正常

### 3. 逐步测试
我们可以逐个模块测试：
1. 先测试串口通信
2. 再测试I2C和MPU6050
3. 然后测试PWM和电机
4. 最后测试整体平衡功能

---

## 🎯 成功标志

当您看到以下现象时，说明系统工作正常：

✅ **诊断全部通过** - 所有8项测试都显示PASS  
✅ **传感器数据正常** - 能看到实时的角度和陀螺仪数据  
✅ **电机响应正常** - 倾斜时电机有反应  
✅ **串口命令有效** - 输入help能看到命令列表  
✅ **平衡车直立** - 能够自主保持平衡  

**现在请编译下载新程序，查看系统诊断报告，告诉我具体哪些测试失败了！** 🔍

---

**排查工程师**: Alex (工程师)  
**排查时间**: 2025-01-15  
**下一步**: 等待诊断报告结果
