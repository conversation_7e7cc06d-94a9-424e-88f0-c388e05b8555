# 🔌 STM32F4 PID平衡车控制系统 - USART1连接指南

## 📋 当前配置

**串口**: USART1  
**引脚**: PA9 (TX), PA10 (RX)  
**波特率**: 115200  
**状态**: ✅ **已配置为USART1**  

---

## 🔌 硬件连接方案

### 标准连接方式
```
STM32F407    →    USB转串口模块
PA9 (TX)     →    RX
PA10 (RX)    →    TX
GND          →    GND
3.3V         →    VCC (如果USB转串口模块需要外部供电)
```

### 重要提醒
⚠️ **注意交叉连接**: STM32的TX连接到USB转串口的RX，STM32的RX连接到USB转串口的TX

---

## 📍 引脚位置确认

### STM32F407VET6 (100引脚LQFP封装)
```
PA9 (USART1_TX): 第68引脚 (右侧，从上往下数第18个)
PA10(USART1_RX): 第69引脚 (右侧，从上往下数第19个)
GND: 多个引脚可选 (如第18、49、74引脚)
3.3V: 第73引脚
```

### STM32F407ZGT6 (144引脚LQFP封装)
```
PA9 (USART1_TX): 第101引脚
PA10(USART1_RX): 第102引脚
GND: 多个引脚可选
3.3V: 第108引脚
```

---

## 🛠️ 常见开发板连接

### 正点原子探索者F407
```
板载串口: USART1 (PA9/PA10)
连接方式: 
1. 直接用USB线连接 (如果有板载USB转串口)
2. 或者外接USB转串口模块到PA9/PA10
注意: 需要安装CH340驱动
```

### STM32F407VET6黑色开发板
```
板载串口: 通常没有板载USB转串口
连接方式: 外接USB转串口模块
推荐模块: CH340, CP2102, FT232
连接: PA9→RX, PA10→TX, GND→GND
```

### 野火F407开发板
```
板载串口: 可能有USART1或USART2
连接方式: 查看开发板丝印和跳线设置
注意: 可能需要设置跳线帽选择串口
```

---

## 🔧 USB转串口模块推荐

### CH340模块 (推荐 - 便宜稳定)
```
特点: 价格便宜，兼容性好
驱动: 需要安装CH340驱动
连接: 3.3V/5V兼容
价格: 5-10元
```

### CP2102模块 (推荐 - 质量好)
```
特点: 质量稳定，信号干净
驱动: 需要安装CP210x驱动
连接: 3.3V供电
价格: 10-15元
```

### FT232模块 (高端选择)
```
特点: 信号质量最好，兼容性最佳
驱动: 通常免驱动
连接: 3.3V/5V可选
价格: 15-25元
```

---

## 📱 串口助手设置

### 基本配置
```
串口: 选择正确的COM口
波特率: 115200
数据位: 8
停止位: 1
校验位: None (无校验)
流控制: None (无流控)
```

### 推荐软件
```
1. SSCOM - 简单易用
2. 串口调试助手 - 功能全面
3. PuTTY - 专业工具
4. Tera Term - 功能强大
```

---

## 🚀 测试步骤

### Step 1: 硬件连接 (5分钟)
1. **断开STM32电源**
2. **连接USB转串口模块**:
   ```
   PA9  → USB转串口的RX
   PA10 → USB转串口的TX
   GND  → GND
   ```
3. **连接USB转串口到电脑**
4. **给STM32上电**

### Step 2: 驱动安装 (3分钟)
1. 插入USB转串口模块
2. 如果提示安装驱动，按提示安装
3. 打开设备管理器确认COM口

### Step 3: 串口测试 (2分钟)
1. 打开串口助手
2. 选择正确的COM口
3. 设置波特率115200
4. 点击"打开串口"

### Step 4: 程序测试 (3分钟)
1. 在Keil中重新编译 (F7)
2. 下载程序 (F8)
3. 观察串口输出

---

## 📊 预期输出

### 正常启动输出
```
UART Test: Hello World!
UART Test: STM32F407 Ready!
UART Test: Using USART1 (PA9/PA10)

=== PID Balance Car System Starting ===
Initializing complete balance control system...

=== Running System Diagnostics ===
Testing system clock...
  SYSCLK: 84000000 Hz
  ✅ Clock frequencies OK

Testing GPIO configuration...
  ✅ Motor direction pins OK

Testing UART communication...
  ✅ UART communication OK

Testing I2C bus...
  Device found at address: 0x68
  ✅ I2C bus OK, 1 device(s) found

Testing MPU6050 sensor...
  ✅ MPU6050 connection OK
  ✅ MPU6050 data reading OK

Testing PWM generation...
  ✅ PWM generation OK

Testing motor control...
  ✅ Motor direction control OK

Testing memory system...
  ✅ Memory system OK

=== DIAGNOSTIC REPORT ===
Total Tests: 8
Passed: 8, Failed: 0, Warnings: 0
Success Rate: 100%

✅ ALL TESTS PASSED - System ready for operation!
```

---

## 🔍 故障排除

### 问题1: 没有任何输出
**检查项目**:
- [ ] PA9连接到USB转串口的RX
- [ ] PA10连接到USB转串口的TX
- [ ] GND连接正确
- [ ] USB转串口驱动已安装
- [ ] COM口选择正确
- [ ] 波特率设置为115200

### 问题2: 输出乱码
**可能原因**:
- 波特率设置错误
- 连接线接触不良
- 时钟配置问题

**解决方案**:
- 确认波特率115200
- 检查连接线质量
- 重新编译下载程序

### 问题3: 输出不稳定
**可能原因**:
- 供电不稳定
- 连接线过长或质量差
- 电磁干扰

**解决方案**:
- 使用稳定的电源
- 使用短而优质的连接线
- 远离干扰源

---

## 🎯 连接验证清单

### 硬件连接检查
- [ ] PA9 (STM32 TX) → RX (USB转串口)
- [ ] PA10 (STM32 RX) → TX (USB转串口)
- [ ] GND → GND
- [ ] 电源连接正常

### 软件设置检查
- [ ] 驱动已正确安装
- [ ] COM口选择正确
- [ ] 波特率设置115200
- [ ] 数据位8，停止位1，无校验

### 程序配置检查
- [ ] 使用USART1 (PA9/PA10)
- [ ] printf重定向到huart1
- [ ] 串口接收中断配置正确

---

## 🏆 成功标志

当您看到以下现象时，说明USART1工作正常：

✅ **看到启动测试信息** - "UART Test: Hello World!"  
✅ **看到引脚信息** - "Using USART1 (PA9/PA10)"  
✅ **看到系统启动信息** - "PID Balance Car System Starting"  
✅ **看到诊断报告** - "System Diagnostics"  
✅ **能发送串口命令** - 输入"help"有响应  

---

## 📞 技术支持

### 驱动下载链接
- **CH340驱动**: [沁恒官网](http://www.wch.cn/downloads/CH341SER_EXE.html)
- **CP210x驱动**: [Silicon Labs官网](https://www.silabs.com/developers/usb-to-uart-bridge-vcp-drivers)
- **FT232驱动**: [FTDI官网](https://ftdichip.com/drivers/vcp-drivers/)

### 常见问题
1. **找不到COM口** → 重新安装驱动
2. **COM口被占用** → 关闭其他串口程序
3. **权限不足** → 以管理员身份运行串口助手

---

**现在程序已经配置为使用USART1 (PA9/PA10)！**

**请按照指南连接硬件，然后重新编译下载程序进行测试！** 🚀

---

**配置工程师**: Alex (工程师)  
**配置时间**: 2025-01-15  
**配置状态**: ✅ **USART1已就绪**
