/**
 ******************************************************************************
 * @file    balance_system.h
 * @brief   平衡车系统集成头文件 - 整合所有控制模块
 * <AUTHOR> (工程师)
 * @version v1.0
 * @date    2025-01-15
 ******************************************************************************
 * @attention
 * 
 * 本文件实现了平衡车系统的完整集成，包括：
 * - PID控制器
 * - 平衡控制器
 * - 电机控制器
 * - 传感器接口
 * - 系统状态管理
 * - 参数调节接口
 * 
 ******************************************************************************
 */

#ifndef __BALANCE_SYSTEM_H
#define __BALANCE_SYSTEM_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "pid_controller.h"
#include "balance_control.h"
#include "motor_control.h"
#include "attitude.h"
#include "mpu6050.h"
#include <stdio.h>

/* Exported types ------------------------------------------------------------*/

/**
 * @brief 系统状态枚举
 */
typedef enum {
    SYSTEM_STATE_INIT = 0,        /**< 初始化状态 */
    SYSTEM_STATE_CALIBRATING = 1, /**< 校准状态 */
    SYSTEM_STATE_READY = 2,       /**< 就绪状态 */
    SYSTEM_STATE_RUNNING = 3,     /**< 运行状态 */
    SYSTEM_STATE_PAUSED = 4,      /**< 暂停状态 */
    SYSTEM_STATE_ERROR = 5,       /**< 错误状态 */
    SYSTEM_STATE_EMERGENCY = 6    /**< 紧急状态 */
} System_State_t;

/**
 * @brief 控制模式枚举
 */
typedef enum {
    CONTROL_MODE_MANUAL = 0,      /**< 手动模式 */
    CONTROL_MODE_AUTO = 1,        /**< 自动平衡模式 */
    CONTROL_MODE_TUNING = 2,      /**< 参数调节模式 */
    CONTROL_MODE_TEST = 3         /**< 测试模式 */
} Control_Mode_t;

/**
 * @brief 系统配置结构体
 */
typedef struct {
    float control_frequency;      /**< 控制频率 (Hz) */
    uint32_t debug_interval;      /**< 调试输出间隔 (ms) */
    uint8_t enable_auto_start;    /**< 自动启动使能 */
    uint8_t enable_safety_check;  /**< 安全检查使能 */
    uint8_t enable_data_logging;  /**< 数据记录使能 */
    float startup_delay;          /**< 启动延迟 (秒) */
} System_Config_t;

/**
 * @brief 系统状态信息结构体
 */
typedef struct {
    System_State_t state;         /**< 系统状态 */
    Control_Mode_t mode;          /**< 控制模式 */
    uint32_t uptime;              /**< 系统运行时间 (ms) */
    uint32_t control_cycles;      /**< 控制周期计数 */
    uint32_t error_count;         /**< 错误计数 */
    uint32_t last_error_time;     /**< 最后错误时间 */
    float cpu_usage;              /**< CPU使用率 (%) */
    uint8_t is_balanced;          /**< 是否平衡 */
    uint32_t balance_time;        /**< 连续平衡时间 (ms) */
} System_Status_t;

/**
 * @brief 性能统计结构体
 */
typedef struct {
    uint32_t loop_time_min;       /**< 最小循环时间 (us) */
    uint32_t loop_time_max;       /**< 最大循环时间 (us) */
    uint32_t loop_time_avg;       /**< 平均循环时间 (us) */
    uint32_t sensor_read_time;    /**< 传感器读取时间 (us) */
    uint32_t control_calc_time;   /**< 控制计算时间 (us) */
    uint32_t motor_update_time;   /**< 电机更新时间 (us) */
    uint32_t total_samples;       /**< 总采样数 */
} Performance_Stats_t;

/**
 * @brief 平衡车系统主结构体
 */
typedef struct {
    // 系统状态
    System_State_t state;
    Control_Mode_t mode;
    System_Config_t config;
    System_Status_t status;
    Performance_Stats_t performance;
    
    // 控制器模块
    Balance_Controller_t balance_controller;
    Motor_Controller_t motor_controller;
    
    // 传感器数据
    MPU6050_Data sensor_data;
    Attitude_Data attitude_data;
    
    // 时间管理
    uint32_t start_time;
    uint32_t last_control_time;
    uint32_t last_debug_time;
    uint32_t last_performance_time;
    
    // 调试和监控
    uint8_t debug_enabled;
    uint8_t performance_monitor_enabled;
    char debug_buffer[256];
} Balance_System_t;

/* Exported constants --------------------------------------------------------*/

/** @defgroup System_Default_Config 默认系统配置
 * @{
 */
#define SYSTEM_DEFAULT_CONTROL_FREQ     200.0f   /**< 默认控制频率 */
#define SYSTEM_DEFAULT_DEBUG_INTERVAL   200      /**< 默认调试间隔 */
#define SYSTEM_DEFAULT_STARTUP_DELAY    2.0f     /**< 默认启动延迟 */
#define SYSTEM_CONTROL_PERIOD_MS        5        /**< 控制周期 */
/**
 * @}
 */

/** @defgroup System_Performance_Limits 性能限制
 * @{
 */
#define SYSTEM_MAX_LOOP_TIME_US         4000     /**< 最大循环时间 */
#define SYSTEM_MAX_CPU_USAGE_PERCENT    80       /**< 最大CPU使用率 */
#define SYSTEM_MIN_CONTROL_FREQ_HZ      150      /**< 最小控制频率 */
/**
 * @}
 */

/* Exported macro ------------------------------------------------------------*/

/**
 * @brief 检查系统是否就绪
 */
#define SYSTEM_IS_READY(sys) \
    ((sys) != NULL && (sys)->state >= SYSTEM_STATE_READY)

/**
 * @brief 检查系统是否运行中
 */
#define SYSTEM_IS_RUNNING(sys) \
    ((sys) != NULL && (sys)->state == SYSTEM_STATE_RUNNING)

/**
 * @brief 微秒计时器 (使用DWT)
 */
#define SYSTEM_GET_US_TICK() \
    (DWT->CYCCNT / (SystemCoreClock / 1000000))

/* Exported functions prototypes ---------------------------------------------*/

/** @defgroup System_Initialization_Functions 系统初始化函数
 * @{
 */
HAL_StatusTypeDef Balance_System_Init(Balance_System_t *system);
HAL_StatusTypeDef Balance_System_SetConfig(Balance_System_t *system, const System_Config_t *config);
HAL_StatusTypeDef Balance_System_SetDefaultConfig(Balance_System_t *system);
HAL_StatusTypeDef Balance_System_Calibrate(Balance_System_t *system);
/**
 * @}
 */

/** @defgroup System_Control_Functions 系统控制函数
 * @{
 */
HAL_StatusTypeDef Balance_System_Start(Balance_System_t *system);
void Balance_System_Stop(Balance_System_t *system);
void Balance_System_Pause(Balance_System_t *system);
void Balance_System_Resume(Balance_System_t *system);
void Balance_System_EmergencyStop(Balance_System_t *system);
void Balance_System_Reset(Balance_System_t *system);
/**
 * @}
 */

/** @defgroup System_Main_Functions 系统主循环函数
 * @{
 */
HAL_StatusTypeDef Balance_System_Update(Balance_System_t *system);
void Balance_System_MainLoop(Balance_System_t *system);
HAL_StatusTypeDef Balance_System_ControlTask(Balance_System_t *system);
/**
 * @}
 */

/** @defgroup System_Parameter_Functions 参数调节函数
 * @{
 */
void Balance_System_SetControlMode(Balance_System_t *system, Control_Mode_t mode);
void Balance_System_SetPIDParams(Balance_System_t *system, float kp, float ki, float kd);
void Balance_System_SetTargetAngle(Balance_System_t *system, float target_angle);
void Balance_System_TunePIDParams(Balance_System_t *system);
/**
 * @}
 */

/** @defgroup System_Status_Functions 状态查询函数
 * @{
 */
System_Status_t* Balance_System_GetStatus(Balance_System_t *system);
System_State_t Balance_System_GetState(const Balance_System_t *system);
Performance_Stats_t* Balance_System_GetPerformance(Balance_System_t *system);
uint8_t Balance_System_IsBalanced(const Balance_System_t *system);
float Balance_System_GetCurrentAngle(const Balance_System_t *system);
float Balance_System_GetControlOutput(const Balance_System_t *system);
/**
 * @}
 */

/** @defgroup System_Debug_Functions 调试和监控函数
 * @{
 */
void Balance_System_EnableDebug(Balance_System_t *system, uint8_t enable);
void Balance_System_PrintStatus(const Balance_System_t *system);
void Balance_System_PrintPerformance(const Balance_System_t *system);
void Balance_System_LogData(Balance_System_t *system);
HAL_StatusTypeDef Balance_System_SelfTest(Balance_System_t *system);
/**
 * @}
 */

/** @defgroup System_Utility_Functions 工具函数
 * @{
 */
const char* Balance_System_GetStateString(System_State_t state);
const char* Balance_System_GetModeString(Control_Mode_t mode);
void Balance_System_UpdatePerformanceStats(Balance_System_t *system, uint32_t loop_time);
/**
 * @}
 */

/* Global system instance ----------------------------------------------------*/
extern Balance_System_t g_balance_system;

#ifdef __cplusplus
}
#endif

#endif /* __BALANCE_SYSTEM_H */

/**
 * @}
 */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
